'use client';

import { useQuery } from '@tanstack/react-query';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { getAllVerifications } from '@/actions/verification';
import { CertificationTable } from './certification-table';
import { CertificationFilters } from './certification-filters';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

export default function CertificationListClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const { status: sessionStatus } = useSession();

  // Obtener parámetros de búsqueda
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
  const query = searchParams.get('query') || undefined;
  const startDate = searchParams.get('startDate') || undefined;
  const endDate = searchParams.get('endDate') || undefined;
  const status = searchParams.get('status') || undefined;

  // Función para actualizar los parámetros de búsqueda
  const updateSearchParams = (params: Record<string, string | undefined>) => {
    const newParams = new URLSearchParams(searchParams.toString());

    // Actualizar o eliminar parámetros
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });

    // Resetear a la página 1 si cambian los filtros
    if (Object.keys(params).some(key => key !== 'page')) {
      newParams.set('page', '1');
    }

    router.push(`${pathname}?${newParams.toString()}`);
  };

  // Consulta para obtener las certificaciones
  const { data, isLoading, isError } = useQuery<{
    verifications: any[];
    pagination: any;
  }>({
    queryKey: ['certifications', page, limit, query, startDate, endDate, status],
    queryFn: async () => {
      const result = await getAllVerifications({
        page,
        limit,
        query,
        startDate,
        endDate,
        status
      });
      return result;
    },
    enabled: sessionStatus === 'authenticated', // Solo ejecutar cuando el usuario esté autenticado
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Manejar errores
  if (isError) {
    toast({
      title: 'Error',
      description: 'No se pudieron cargar las certificaciones',
      variant: 'destructive'
    });
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Listado de Certificaciones</h1>
      </div>

      <CertificationFilters
        initialQuery={query}
        initialStartDate={startDate}
        initialEndDate={endDate}
        initialStatus={status}
        onFilterChange={updateSearchParams}
      />

      {isLoading ? (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Cargando certificaciones...</span>
        </div>
      ) : isError ? (
        <div className="text-center py-10 text-red-500">
          Error al cargar las certificaciones. Por favor, intenta de nuevo.
        </div>
      ) : (
        <CertificationTable
          data={data?.verifications || []}
          pagination={data?.pagination}
          onPageChange={(newPage) => updateSearchParams({ page: newPage.toString() })}
        />
      )}
    </div>
  );
}