'use client';

import { useState } from 'react';
import { DataTableV2 } from '@/components/DataTableV2';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { VerificationRecord } from '@/types';

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface CertificationTableProps {
  data: VerificationRecord[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
}

export function CertificationTable({
  data,
  pagination,
  onPageChange
}: CertificationTableProps) {
  // Función para obtener el badge según el estado
  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completada</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pendiente</Badge>;
      case 'expired':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Vencida</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Cancelada</Badge>;
      default:
        return <Badge variant="outline">{status || 'N/A'}</Badge>;
    }
  };

  // Función para obtener el badge del tipo de holograma
  const getHologramBadge = (hologramType?: string) => {
    switch (hologramType) {
      case '00':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Exento</Badge>;
      case '0':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Cumple</Badge>;
      case '1':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Primera</Badge>;
      case '2':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Segunda</Badge>;
      default:
        return <Badge variant="outline">{hologramType || 'N/A'}</Badge>;
    }
  };

  // Definir las columnas de la tabla
  const columns: ColumnDef<VerificationRecord, any>[] = [
    {
      accessorKey: 'vehiclePlate',
      header: 'Placas',
      cell: ({ row }) => {
        const plate = row.original.vehiclePlate || 'N/A';
        return <div className="font-medium">{plate}</div>;
      },
    },
    {
      accessorKey: 'verificationCenterId',
      header: 'Verificentro',
      cell: ({ row }) => {
        return <div>{row.original.verificationCenterId || 'N/A'}</div>;
      },
    },
    {
      accessorKey: 'verificationDate',
      header: 'Fecha de Verificación',
      cell: ({ row }) => {
        const date = row.original.verificationDate;
        return <div>{date ? formatDate(new Date(date)) : 'N/A'}</div>;
      },
    },
    {
      accessorKey: 'hologramType',
      header: 'Tipo de Holograma',
      cell: ({ row }) => {
        return getHologramBadge(row.original.hologramType);
      },
    },
    {
      accessorKey: 'isExempt',
      header: 'Exento',
      cell: ({ row }) => {
        const isExempt = row.original.isExempt;
        return (
          <Badge variant={isExempt ? "default" : "outline"}>
            {isExempt ? 'Sí' : 'No'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Estado',
      cell: ({ row }) => {
        return getStatusBadge(row.original.status);
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Fecha de Registro',
      cell: ({ row }) => {
        const date = row.original.createdAt;
        return <div>{date ? formatDate(new Date(date)) : 'N/A'}</div>;
      },
    },
    {
      id: 'actions',
      header: 'Acciones',
      cell: ({ row }) => {
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              // Aquí podrías abrir un modal de detalles si es necesario
              console.log('Ver detalles de certificación:', row.original);
            }}
          >
            <Eye className="h-4 w-4 mr-1" />
            Ver
          </Button>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      <DataTableV2
        columns={columns}
        data={data}
        total={pagination?.total || 0}
        allRecords={pagination?.total || 0}
        onPageChange={async () => { }}
        lenguage="es"
      />

      {pagination && pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Mostrando {data.length} de {pagination.total} resultados
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                // Mostrar páginas alrededor de la página actual
                let pageNumber: number;

                if (pagination.pages <= 5) {
                  // Si hay 5 o menos páginas, mostrar todas
                  pageNumber = i + 1;
                } else if (pagination.page <= 3) {
                  // Si estamos en las primeras páginas
                  pageNumber = i + 1;
                } else if (pagination.page >= pagination.pages - 2) {
                  // Si estamos en las últimas páginas
                  pageNumber = pagination.pages - 4 + i;
                } else {
                  // Si estamos en el medio
                  pageNumber = pagination.page - 2 + i;
                }

                return (
                  <Button
                    key={pageNumber}
                    variant={pagination.page === pageNumber ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(pageNumber)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNumber}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}