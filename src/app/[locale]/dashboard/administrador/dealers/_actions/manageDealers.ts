'use server';

import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants/api';
import { CreateDealerRequest, UpdateDealerRequest, DealerResponse } from '../types';
import { revalidatePath } from 'next/cache';

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export async function createDealer(request: CreateDealerRequest): Promise<ApiResponse> {
  console.log('🔵 [DEBUG] createDealer iniciado');
  console.log('📋 [DEBUG] Request data:', JSON.stringify(request, null, 2));

  try {
    const user = await getCurrentUser();
    console.log('👤 [DEBUG] User data:', {
      id: user?._id,
      email: user?.email,
      role: user?.role,
      tokenExists: !!user?.accessToken,
      tokenLength: user?.accessToken?.length
    });

    if (!user) {
      console.log('❌ [DEBUG] No user found');
      return { success: false, error: 'No user found' };
    }

    const url = `${URL_API}/vendor-platform/fleet-orders/dealers`;
    console.log('🌐 [DEBUG] API URL:', url);
    console.log('🔑 [DEBUG] Headers:', {
      'Authorization': `Bearer ${user.accessToken?.substring(0, 20)}...`,
      'Content-Type': 'application/json'
    });

    // Simulate curl command
    console.log('📋 [DEBUG] Equivalent curl command:');
    console.log(`curl -X POST "${url}" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${user.accessToken}" \\`);
    console.log(`  -d '${JSON.stringify(request, null, 2)}'`);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    console.log('📡 [DEBUG] Response status:', response.status);
    console.log('📡 [DEBUG] Response headers:', Object.fromEntries(response.headers));

    if (!response.ok) {
      console.log('❌ [DEBUG] Response not OK');
      const errorData = await response.json().catch(() => null);
      console.log('❌ [DEBUG] Error data:', errorData);

      // If endpoint doesn't exist (404), show helpful message
      if (response.status === 404) {
        console.log('❌ [DEBUG] 404 - Endpoint not found');
        return {
          success: false,
          error: 'Endpoint no encontrado. Verifica que el backend tenga implementados los endpoints de dealers.'
        };
      }

      if (response.status === 401) {
        console.log('❌ [DEBUG] 401 - Unauthorized. Token might be invalid or expired');
      }

      const errorMessage = errorData?.message || `HTTP ${response.status}: ${response.statusText}`;
      console.log('❌ [DEBUG] Final error message:', errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    }

    const data = await response.json();
    console.log('✅ [DEBUG] Success response data:', data);

    // Revalidate the dealers page to show fresh data
    revalidatePath('/dashboard/administrador/dealers');
    console.log('🔄 [DEBUG] Path revalidated');

    return { success: true, data: data.data };
  } catch (error) {
    console.log('💥 [DEBUG] Exception caught:', error);
    console.error('Error creating dealer:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

export async function updateDealer(id: string, request: UpdateDealerRequest): Promise<ApiResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'No user found' };
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/dealers/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);

      // If endpoint doesn't exist (404), show helpful message
      if (response.status === 404) {
        return {
          success: false,
          error: 'Endpoint no encontrado. Verifica que el backend tenga implementados los endpoints de dealers.'
        };
      }

      return {
        success: false,
        error: errorData?.message || `HTTP ${response.status}: ${response.statusText}`
      };
    }

    const data = await response.json();

    // Revalidate the dealers page to show fresh data
    revalidatePath('/dashboard/administrador/dealers');
    revalidatePath(`/dashboard/administrador/dealers/${id}`);

    return { success: true, data: data.data };
  } catch (error) {
    console.error('Error updating dealer:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

export async function deleteDealer(id: string): Promise<ApiResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'No user found' };
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/dealers/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);

      // If endpoint doesn't exist (404), show helpful message
      if (response.status === 404) {
        return {
          success: false,
          error: 'Endpoint no encontrado. Verifica que el backend tenga implementados los endpoints de dealers.'
        };
      }

      return {
        success: false,
        error: errorData?.message || `HTTP ${response.status}: ${response.statusText}`
      };
    }

    // Revalidate the dealers page to show fresh data
    revalidatePath('/dashboard/administrador/dealers');

    return { success: true };
  } catch (error) {
    console.error('Error deleting dealer:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}