import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants/api';
import { Dealer, DealerFilters, DealersResponse, BrandsResponse } from '../types';

export async function getDealers(filters: DealerFilters = {}): Promise<DealersResponse | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const searchParams = new URLSearchParams();

    // Add filters to search params
    if (filters.brand) searchParams.append('brand', filters.brand);
    if (filters.isActive !== undefined) searchParams.append('isActive', filters.isActive.toString());
    if (filters.page) searchParams.append('page', filters.page.toString());
    if (filters.limit) searchParams.append('limit', filters.limit.toString());
    if (filters.search) searchParams.append('search', filters.search);

    const url = `${URL_API}/vendor-platform/fleet-orders/dealers?${searchParams.toString()}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Ensure fresh data
    });

    if (!response.ok) {
      console.error('Failed to fetch dealers:', response.status, response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching dealers:', error);
    return null;
  }
}

export async function getDealerById(id: string): Promise<Dealer | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/dealers/${id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error('Failed to fetch dealer:', response.status);
      return null;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching dealer:', error);
    return null;
  }
}

export async function getDealersByBrand(brand: string): Promise<Dealer[] | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/dealers/brand/${brand}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error('Failed to fetch dealers by brand:', response.status);
      return null;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching dealers by brand:', error);
    return null;
  }
}

export async function getAvailableBrands(): Promise<string[] | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/brands`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error('Failed to fetch brands:', response.status, response.statusText);
      return null;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching brands:', error);
    return null;
  }
}