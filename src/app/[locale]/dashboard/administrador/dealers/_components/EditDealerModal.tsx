'use client';

import { useState, useTransition, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Trash2, Building, Mail, Phone, MapPin } from 'lucide-react';
import { updateDealer } from '../_actions/manageDealers';
import { Dealer, UpdateDealerRequest } from '../types';
import { toast } from '@/components/ui/use-toast';

const dealerSchema = z.object({
  name: z.string().min(1, 'Nombre requerido'),
  brands: z.array(z.string()).min(1, 'Debe agregar al menos una marca'),
  contactEmail: z.string().email('Email inválido'),
  contactPhone: z.string().min(1, 'Teléfono requerido'),
  address: z.string().min(1, 'Dirección requerida'),
  city: z.string().min(1, 'Ciudad requerida'),
  state: z.string().min(1, 'Estado requerido'),
  country: z.string().min(1, 'País requerido'),
});

type FormData = z.infer<typeof dealerSchema>;

interface EditDealerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  dealer: Dealer | null;
  availableBrands?: string[];
}

export function EditDealerModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  dealer,
  availableBrands = [] 
}: EditDealerModalProps) {
  const [isPending, startTransition] = useTransition();
  const [brandInput, setBrandInput] = useState('');
  
  const form = useForm<FormData>({
    resolver: zodResolver(dealerSchema),
    defaultValues: {
      name: '',
      brands: [],
      contactEmail: '',
      contactPhone: '',
      address: '',
      city: '',
      state: '',
      country: 'Mexico',
    },
  });

  // Update form when dealer changes
  useEffect(() => {
    if (dealer) {
      form.reset({
        name: dealer.name,
        brands: dealer.brands,
        contactEmail: dealer.contactEmail,
        contactPhone: dealer.contactPhone,
        address: dealer.address,
        city: dealer.city,
        state: dealer.state,
        country: dealer.country,
      });
    }
  }, [dealer, form]);

  const watchedBrands = form.watch('brands');

  // Common Mexican states
  const mexicanStates = [
    'Aguascalientes', 'Baja California', 'Baja California Sur', 'Campeche', 'Chiapas', 'Chihuahua',
    'Ciudad de México', 'Coahuila', 'Colima', 'Durango', 'Guanajuato', 'Guerrero', 'Hidalgo',
    'Jalisco', 'México', 'Michoacán', 'Morelos', 'Nayarit', 'Nuevo León', 'Oaxaca', 'Puebla',
    'Querétaro', 'Quintana Roo', 'San Luis Potosí', 'Sinaloa', 'Sonora', 'Tabasco', 'Tamaulipas',
    'Tlaxcala', 'Veracruz', 'Yucatán', 'Zacatecas'
  ];

  // Default available brands if not provided
  const defaultBrands = ['BYD', 'MG', 'CHERY', 'TESLA', 'Toyota', 'Nissan', 'Volkswagen', 'Chevrolet', 'Ford'];
  const brandsToShow = availableBrands.length > 0 ? availableBrands : defaultBrands;

  const handleAddBrand = (brand: string) => {
    if (brand && !watchedBrands.includes(brand)) {
      form.setValue('brands', [...watchedBrands, brand]);
    }
  };

  const handleAddCustomBrand = () => {
    if (brandInput && !watchedBrands.includes(brandInput)) {
      form.setValue('brands', [...watchedBrands, brandInput]);
      setBrandInput('');
    }
  };

  const handleRemoveBrand = (index: number) => {
    const newBrands = watchedBrands.filter((_, i) => i !== index);
    form.setValue('brands', newBrands);
  };

  const onSubmit = (data: FormData) => {
    if (!dealer) return;
    
    startTransition(async () => {
      try {
        const request: UpdateDealerRequest = {
          name: data.name,
          brands: data.brands,
          contactEmail: data.contactEmail,
          contactPhone: data.contactPhone,
          address: data.address,
          city: data.city,
          state: data.state,
          country: data.country,
        };

        const result = await updateDealer(dealer._id, request);
        
        if (result.success) {
          toast({
            title: "Éxito",
            description: "Dealer actualizado exitosamente",
          });
          onSuccess();
          onClose();
        } else {
          toast({
            title: "Error",
            description: result.error || 'Error al actualizar el dealer',
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: 'Error inesperado al actualizar el dealer',
          variant: "destructive",
        });
        console.error('Error updating dealer:', error);
      }
    });
  };

  if (!dealer) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building size={20} />
            Editar Dealer - {dealer.name}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información Básica</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre del Dealer</Label>
                  <Input
                    id="name"
                    {...form.register('name')}
                    placeholder="AutoMax Mexico"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">País</Label>
                  <Input
                    id="country"
                    {...form.register('country')}
                    placeholder="Mexico"
                  />
                  {form.formState.errors.country && (
                    <p className="text-sm text-red-600">{form.formState.errors.country.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Mail size={18} />
                Información de Contacto
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Email de Contacto</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    {...form.register('contactEmail')}
                    placeholder="<EMAIL>"
                  />
                  {form.formState.errors.contactEmail && (
                    <p className="text-sm text-red-600">{form.formState.errors.contactEmail.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPhone">Teléfono de Contacto</Label>
                  <Input
                    id="contactPhone"
                    {...form.register('contactPhone')}
                    placeholder="+52-55-1234-5678"
                  />
                  {form.formState.errors.contactPhone && (
                    <p className="text-sm text-red-600">{form.formState.errors.contactPhone.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin size={18} />
                Dirección
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="address">Dirección</Label>
                  <Input
                    id="address"
                    {...form.register('address')}
                    placeholder="Av. Reforma 123, Col. Centro"
                  />
                  {form.formState.errors.address && (
                    <p className="text-sm text-red-600">{form.formState.errors.address.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">Ciudad</Label>
                    <Input
                      id="city"
                      {...form.register('city')}
                      placeholder="Ciudad de México"
                    />
                    {form.formState.errors.city && (
                      <p className="text-sm text-red-600">{form.formState.errors.city.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">Estado</Label>
                    <Input
                      id="state"
                      {...form.register('state')}
                      placeholder="Ciudad de México"
                      list="states"
                    />
                    <datalist id="states">
                      {mexicanStates.map((state) => (
                        <option key={state} value={state} />
                      ))}
                    </datalist>
                    {form.formState.errors.state && (
                      <p className="text-sm text-red-600">{form.formState.errors.state.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Brands */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Marcas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Marcas Disponibles</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {brandsToShow.map((brand) => (
                      <Button
                        key={brand}
                        type="button"
                        variant={watchedBrands.includes(brand) ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleAddBrand(brand)}
                        disabled={watchedBrands.includes(brand)}
                      >
                        {brand}
                        {watchedBrands.includes(brand) && (
                          <span className="ml-1">✓</span>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Agregar Marca Personalizada</Label>
                  <div className="flex gap-2 mt-2">
                    <Input
                      placeholder="Nombre de la marca"
                      value={brandInput}
                      onChange={(e) => setBrandInput(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddCustomBrand();
                        }
                      }}
                    />
                    <Button type="button" onClick={handleAddCustomBrand} variant="outline">
                      <Plus size={16} />
                    </Button>
                  </div>
                </div>

                {watchedBrands.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">Marcas Seleccionadas</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {watchedBrands.map((brand, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-2">
                          {brand}
                          <Button
                            type="button"
                            onClick={() => handleRemoveBrand(index)}
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 ml-1"
                          >
                            <Trash2 size={12} />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {form.formState.errors.brands && (
                  <p className="text-sm text-red-600">{form.formState.errors.brands.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isPending}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isPending} className="bg-blue-600 hover:bg-blue-700">
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Actualizando...
                </>
              ) : (
                'Actualizar Dealer'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}