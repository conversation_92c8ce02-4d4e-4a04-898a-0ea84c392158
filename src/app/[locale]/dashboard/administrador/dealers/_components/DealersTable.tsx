'use client';

import { useState } from 'react';
import { DataTableV2 } from '@/components/DataTableV2';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Edit, Trash2, ChevronLeft, ChevronRight, Building, Mail, Phone } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { Dealer } from '../types';

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface DealersTableProps {
  data: Dealer[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onRowClick: (dealer: Dealer) => void;
  onEdit: (dealer: Dealer) => void;
  onDelete: (dealer: Dealer) => void;
}

export function DealersTable({
  data,
  pagination,
  onPageChange,
  onRowClick,
  onEdit,
  onDelete
}: DealersTableProps) {
  const getBrandsBadges = (brands: string[]) => {
    return (
      <div className="flex flex-wrap gap-1">
        {brands.slice(0, 3).map((brand) => (
          <Badge key={brand} variant="secondary" className="text-xs">
            {brand}
          </Badge>
        ))}
        {brands.length > 3 && (
          <Badge variant="outline" className="text-xs">
            +{brands.length - 3}
          </Badge>
        )}
      </div>
    );
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge 
        variant="outline" 
        className={isActive ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200'}
      >
        {isActive ? 'Activo' : 'Inactivo'}
      </Badge>
    );
  };

  const columns: ColumnDef<Dealer, any>[] = [
    {
      accessorKey: 'name',
      header: 'Nombre',
      cell: ({ row }) => {
        const name = row.getValue('name') as string;
        const isActive = row.original.isActive;
        return (
          <div className="flex items-center gap-2">
            <Building size={16} className={isActive ? 'text-green-600' : 'text-gray-400'} />
            <div>
              <p className="font-medium">{name}</p>
              <p className="text-xs text-gray-500">{row.original.city}, {row.original.state}</p>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'brands',
      header: 'Marcas',
      cell: ({ row }) => {
        const brands = row.getValue('brands') as string[];
        return getBrandsBadges(brands);
      },
    },
    {
      accessorKey: 'contactEmail',
      header: 'Contacto',
      cell: ({ row }) => {
        const email = row.getValue('contactEmail') as string;
        const phone = row.original.contactPhone;
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm">
              <Mail size={12} className="text-gray-400" />
              <span className="truncate max-w-[150px]">{email}</span>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Phone size={12} className="text-gray-400" />
              <span>{phone}</span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'Estado',
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as boolean;
        return getStatusBadge(isActive);
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Fecha de Creación',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(new Date(date))}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Acciones',
      cell: ({ row }) => {
        const dealer = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onRowClick(dealer);
              }}
              title="Ver detalles"
            >
              <Eye size={16} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(dealer);
              }}
              title="Editar"
            >
              <Edit size={16} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(dealer);
              }}
              className="text-red-600 hover:text-red-700"
              title="Eliminar"
            >
              <Trash2 size={16} />
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      <DataTableV2
        columns={columns as any}
        data={data}
        onRowClick={(row: Dealer) => onRowClick(row)}
      />
      
      {/* Custom Pagination */}
      <div className="flex items-center justify-between px-4 py-2">
        <div className="text-sm text-gray-600">
          Mostrando {((pagination.currentPage - 1) * 10) + 1} a{' '}
          {Math.min(pagination.currentPage * 10, pagination.totalItems)} de{' '}
          {pagination.totalItems} dealers
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrev}
          >
            <ChevronLeft size={16} />
            Anterior
          </Button>
          
          <span className="text-sm font-medium">
            Página {pagination.currentPage} de {pagination.totalPages}
          </span>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNext}
          >
            Siguiente
            <ChevronRight size={16} />
          </Button>
        </div>
      </div>
    </div>
  );
}