import { Suspense } from 'react';
import { getDealers, getAvailableBrands } from './_actions/getDealers';
import DealersClient from './client';
import { DealerFilters } from './types';

interface DealersPageProps {
  searchParams: {
    brand?: string;
    isActive?: string;
    page?: string;
    limit?: string;
    search?: string;
  };
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex justify-between items-center">
        <div>
          <div className="h-8 w-32 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div className="h-4 w-64 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="p-6 border rounded-lg">
            <div className="flex justify-between items-start mb-4">
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>

      {/* Filters Skeleton */}
      <div className="p-4 border rounded-lg">
        <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-10 w-full bg-gray-200 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Table Skeleton */}
      <div className="border rounded-lg">
        <div className="p-4">
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex space-x-4">
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-28 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

async function DealersContent({ searchParams }: DealersPageProps) {
  // Parse search parameters into filters
  const filters: DealerFilters = {
    brand: searchParams.brand,
    isActive: searchParams.isActive ? searchParams.isActive === 'true' : undefined,
    page: searchParams.page ? parseInt(searchParams.page) : 1,
    limit: searchParams.limit ? parseInt(searchParams.limit) : 10,
    search: searchParams.search,
  };

  // Fetch data in parallel
  const [dealersData, availableBrands] = await Promise.all([
    getDealers(filters),
    getAvailableBrands()
  ]);

  return (
    <DealersClient
      initialData={dealersData}
      initialFilters={filters}
      availableBrands={availableBrands || []}
    />
  );
}

export default function DealersPage({ searchParams }: DealersPageProps) {
  return (
    <section className="flex flex-col min-h-[90vh] p-6">
      <Suspense fallback={<LoadingSkeleton />}>
        <DealersContent searchParams={searchParams} />
      </Suspense>
    </section>
  );
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;