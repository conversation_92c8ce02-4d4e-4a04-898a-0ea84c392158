'use client';

import { useState, useCallback, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Building, TrendingUp, AlertTriangle, Users, Info } from 'lucide-react';
import { DealersTable } from './_components/DealersTable';
import { DealersFiltersComponent } from './_components/DealersFilters';
import { CreateDealerModal } from './_components/CreateDealerModal';
import { Dealer, DealerFilters, DealersResponse } from './types';
import { useRouter, useSearchParams } from 'next/navigation';
import { deleteDealer } from './_actions/manageDealers';
import { toast } from '@/components/ui/use-toast';

interface DealersClientProps {
  initialData: DealersResponse | null;
  initialFilters: DealerFilters;
  availableBrands?: string[];
}

export default function DealersClient({ 
  initialData, 
  initialFilters,
  availableBrands = [] 
}: DealersClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  const [data, setData] = useState<DealersResponse | null>(initialData);
  const [filters, setFilters] = useState<DealerFilters>(initialFilters);
  const [selectedDealer, setSelectedDealer] = useState<Dealer | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Statistics from the data
  const totalDealers = data?.pagination.totalItems || 0;
  const activeDealers = data?.data.filter(dealer => dealer.isActive).length || 0;
  const inactiveDealers = totalDealers - activeDealers;
  const totalBrands = new Set(data?.data.flatMap(dealer => dealer.brands) || []).size;
  
  // Check if there's no data (endpoints not available)
  const noDataAvailable = !data || data.data.length === 0;

  const updateURLWithFilters = useCallback((newFilters: DealerFilters) => {
    const params = new URLSearchParams(searchParams);
    
    // Update URL parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      } else {
        params.delete(key);
      }
    });

    startTransition(() => {
      router.push(`?${params.toString()}`, { scroll: false });
    });
  }, [searchParams, router]);

  const handleFiltersChange = useCallback((newFilters: DealerFilters) => {
    setFilters(newFilters);
    updateURLWithFilters(newFilters);
  }, [updateURLWithFilters]);

  const handleClearFilters = useCallback(() => {
    const clearedFilters: DealerFilters = { page: 1, limit: 10 };
    setFilters(clearedFilters);
    updateURLWithFilters(clearedFilters);
  }, [updateURLWithFilters]);

  const handlePageChange = useCallback((page: number) => {
    const newFilters = { ...filters, page };
    handleFiltersChange(newFilters);
  }, [filters, handleFiltersChange]);

  const handleRowClick = useCallback((dealer: Dealer) => {
    // Navigate to dealer details page
    router.push(`/dashboard/administrador/dealers/${dealer._id}`);
  }, [router]);

  const handleEdit = useCallback((dealer: Dealer) => {
    setSelectedDealer(dealer);
    // TODO: Open edit modal
  }, []);

  const handleDelete = useCallback(async (dealer: Dealer) => {
    if (window.confirm(`¿Estás seguro de que deseas eliminar el dealer "${dealer.name}"?`)) {
      try {
        const result = await deleteDealer(dealer._id);
        
        if (result.success) {
          toast({
            title: "Éxito",
            description: "Dealer eliminado exitosamente",
          });
          // Refresh the page to get updated data
          window.location.reload();
        } else {
          toast({
            title: "Error",
            description: result.error || 'Error al eliminar el dealer',
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: 'Error inesperado al eliminar el dealer',
          variant: "destructive",
        });
        console.error('Error deleting dealer:', error);
      }
    }
  }, []);

  const handleCreateDealer = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleModalSuccess = useCallback(() => {
    // Refresh the page to get updated data
    window.location.reload();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Dealers</h1>
          <p className="text-gray-600 mt-2">
            Gestión de dealers y concesionarios
          </p>
        </div>
        
        <Button onClick={handleCreateDealer} className="bg-blue-600 hover:bg-blue-700">
          <Plus size={16} className="mr-2" />
          Nuevo Dealer
        </Button>
      </div>

      {/* No Data Alert */}
      {!data && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Endpoints no disponibles
                </p>
                <p className="text-sm text-yellow-700">
                  No se pudieron cargar los dealers. Verifica que el backend esté funcionando y tenga los endpoints implementados.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Dealers</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDealers}</div>
            <p className="text-xs text-muted-foreground">
              En el sistema
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dealers Activos</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeDealers}</div>
            <p className="text-xs text-muted-foreground">
              Disponibles para órdenes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dealers Inactivos</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{inactiveDealers}</div>
            <p className="text-xs text-muted-foreground">
              Suspendidos o eliminados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Marcas Disponibles</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{totalBrands}</div>
            <p className="text-xs text-muted-foreground">
              Marcas diferentes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <DealersFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
        availableBrands={availableBrands}
      />

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          {data && data.data.length > 0 ? (
            <DealersTable
              data={data.data}
              pagination={data.pagination}
              onPageChange={handlePageChange}
              onRowClick={handleRowClick}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ) : (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-4">
                <Building size={48} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No hay dealers
              </h3>
              <p className="text-gray-600 mb-4">
                Comienza agregando tu primer dealer o concesionario.
              </p>
              <Button onClick={handleCreateDealer} className="bg-blue-600 hover:bg-blue-700">
                <Plus size={16} className="mr-2" />
                Crear Primer Dealer
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading overlay */}
      {isPending && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </div>
      )}

      {/* Modals */}
      <CreateDealerModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleModalSuccess}
        availableBrands={availableBrands}
      />
    </div>
  );
}