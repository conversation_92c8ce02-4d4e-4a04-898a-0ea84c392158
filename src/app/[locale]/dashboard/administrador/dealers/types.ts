export interface Dealer {
  _id: string;
  name: string;
  brands: string[];
  contactEmails: string[];
  contactPhone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDealerRequest {
  name: string;
  brands: string[];
  contactEmails: string[];
  contactPhone: string;
  address: string;
  city: string;
  state: string;
  country: string;
}

export interface UpdateDealerRequest extends Partial<CreateDealerRequest> {}

export interface DealerFilters {
  brand?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
  search?: string;
}

export interface DealersResponse {
  message: string;
  data: Dealer[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface DealerResponse {
  message: string;
  data: Dealer;
}

export interface BrandsResponse {
  message: string;
  data: string[];
}