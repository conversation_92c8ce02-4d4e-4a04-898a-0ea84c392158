'use server';

import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants/api';
import { CreateFleetOrderRequest, FleetOrder } from '../types';
import { revalidatePath } from 'next/cache';

interface CreateFleetOrderResponse {
  success: boolean;
  data?: FleetOrder;
  error?: string;
}

export async function createFleetOrder(request: CreateFleetOrderRequest): Promise<CreateFleetOrderResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'No user found' };
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return { 
        success: false, 
        error: errorData?.message || `HTTP ${response.status}: ${response.statusText}` 
      };
    }

    const data = await response.json();
    
    // Revalidate the fleet orders page to show fresh data
    revalidatePath('/dashboard/administrador/fleet-orders');
    
    return { success: true, data: data.data };
  } catch (error) {
    console.error('Error creating fleet order:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}