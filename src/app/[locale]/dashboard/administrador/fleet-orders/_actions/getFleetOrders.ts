import { cache } from 'react';
import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants/api';
import { FleetOrder, FleetOrderFilters } from '../types';

interface FleetOrdersResponse {
  data: FleetOrder[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const getFleetOrders = cache(async (filters: FleetOrderFilters = {}): Promise<FleetOrdersResponse | null> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const searchParams = new URLSearchParams();
    
    // Add filters to search params
    if (filters.status) searchParams.append('status', filters.status);
    if (filters.year) searchParams.append('year', filters.year.toString());
    if (filters.month) searchParams.append('month', filters.month.toString());
    if (filters.page) searchParams.append('page', filters.page.toString());
    if (filters.limit) searchParams.append('limit', filters.limit.toString());
    if (filters.sortBy) searchParams.append('sortBy', filters.sortBy);
    if (filters.sortOrder) searchParams.append('sortOrder', filters.sortOrder);

    const url = `${URL_API}/vendor-platform/fleet-orders?${searchParams.toString()}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Ensure fresh data
    });

    if (!response.ok) {
      console.error('Failed to fetch fleet orders:', response.status);
      return null;
    }

    const data = await response.json();
    console.log('Fleet orders API response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching fleet orders:', error);
    return null;
  }
});

export const getFleetOrderById = cache(async (id: string): Promise<FleetOrder | null> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/${id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error('Failed to fetch fleet order:', response.status);
      return null;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching fleet order:', error);
    return null;
  }
});

export const getFleetOrderByNumber = cache(async (orderNumber: string): Promise<FleetOrder | null> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/number/${orderNumber}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error('Failed to fetch fleet order by number:', response.status);
      return null;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching fleet order by number:', error);
    return null;
  }
});