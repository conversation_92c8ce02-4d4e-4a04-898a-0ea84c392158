'use server';

import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants/api';
import { UpdateFleetOrderStatusRequest, UpdateDispersionRequest, FleetOrder } from '../types';
import { revalidatePath } from 'next/cache';

interface UpdateFleetOrderResponse {
  success: boolean;
  data?: FleetOrder;
  error?: string;
}

export async function updateFleetOrderStatus(
  id: string, 
  request: UpdateFleetOrderStatusRequest
): Promise<UpdateFleetOrderResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'No user found' };
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/${id}/status`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return { 
        success: false, 
        error: errorData?.message || `HTTP ${response.status}: ${response.statusText}` 
      };
    }

    const data = await response.json();
    
    // Revalidate the fleet orders page to show fresh data
    revalidatePath('/dashboard/administrador/fleet-orders');
    revalidatePath(`/dashboard/administrador/fleet-orders/${id}`);
    
    return { success: true, data: data.data };
  } catch (error) {
    console.error('Error updating fleet order status:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

export async function updateFleetOrderDispersion(
  id: string, 
  request: UpdateDispersionRequest
): Promise<UpdateFleetOrderResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'No user found' };
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/${id}/dispersion`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return { 
        success: false, 
        error: errorData?.message || `HTTP ${response.status}: ${response.statusText}` 
      };
    }

    const data = await response.json();
    
    // Revalidate the fleet orders page to show fresh data
    revalidatePath('/dashboard/administrador/fleet-orders');
    revalidatePath(`/dashboard/administrador/fleet-orders/${id}`);
    
    return { success: true, data: data.data };
  } catch (error) {
    console.error('Error updating fleet order dispersion:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

export async function deleteFleetOrder(id: string): Promise<UpdateFleetOrderResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'No user found' };
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return { 
        success: false, 
        error: errorData?.message || `HTTP ${response.status}: ${response.statusText}` 
      };
    }

    // Revalidate the fleet orders page to show fresh data
    revalidatePath('/dashboard/administrador/fleet-orders');
    
    return { success: true };
  } catch (error) {
    console.error('Error deleting fleet order:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}