import { cache } from 'react';
import getCurrentUser from '@/actions/getCurrentUser';
import { URL_API } from '@/constants/api';
import { SLAAlert } from '../types';

interface SLAAlertsResponse {
  data: SLAAlert[];
  total: number;
}

interface SLAStatistics {
  totalOrders: number;
  onTimeOrders: number;
  overdueOrders: number;
  warningOrders: number;
  averageCompletionTime: number;
  slaComplianceRate: number;
}

export const getSLAAlerts = cache(async (): Promise<SLAAlertsResponse | null> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/sla/alerts`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Ensure fresh data for real-time alerts
    });

    if (!response.ok) {
      console.error('Failed to fetch SLA alerts:', response.status);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching SLA alerts:', error);
    return null;
  }
});

export const getSLAStatistics = cache(async (): Promise<SLAStatistics | null> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      console.error('No user found');
      return null;
    }

    const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/sla/statistics`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error('Failed to fetch SLA statistics:', response.status);
      return null;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching SLA statistics:', error);
    return null;
  }
});