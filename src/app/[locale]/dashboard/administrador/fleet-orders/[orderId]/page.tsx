import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { getFleetOrderById } from '../_actions/getFleetOrders';
import FleetOrderDetailsClient from './client';

interface FleetOrderDetailsPageProps {
  params: {
    orderId: string;
  };
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex justify-between items-center">
        <div>
          <div className="h-8 w-64 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div className="h-4 w-96 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="flex gap-2">
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Cards Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-6 border rounded-lg">
              <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
        <div className="space-y-6">
          {[1, 2].map((i) => (
            <div key={i} className="p-6 border rounded-lg">
              <div className="h-6 w-24 bg-gray-200 rounded animate-pulse mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-2/3 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

async function FleetOrderDetailsContent({ params }: FleetOrderDetailsPageProps) {
  const order = await getFleetOrderById(params.orderId);

  if (!order) {
    notFound();
  }

  return <FleetOrderDetailsClient order={order} />;
}

export default function FleetOrderDetailsPage({ params }: FleetOrderDetailsPageProps) {
  return (
    <section className="flex flex-col min-h-[90vh] p-6">
      <Suspense fallback={<LoadingSkeleton />}>
        <FleetOrderDetailsContent params={params} />
      </Suspense>
    </section>
  );
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;