'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  Edit, 
  MapPin, 
  Calendar, 
  Mail, 
  Car, 
  AlertCircle,
  CheckCircle,
  FileText,
  Camera,
  Clock
} from 'lucide-react';
import { UpdateStatusModal } from '../_components/UpdateStatusModal';
import { DispersionModal } from '../_components/DispersionModal';
import { FleetOrder, STATUS_LABELS, STATUS_COLORS } from '../types';
import { formatDate } from '@/lib/utils';

interface FleetOrderDetailsClientProps {
  order: FleetOrder;
}

export default function FleetOrderDetailsClient({ order }: FleetOrderDetailsClientProps) {
  const router = useRouter();
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showDispersionModal, setShowDispersionModal] = useState(false);
  const [mounted, setMounted] = useState(false);

  // All callbacks must be defined before any conditional returns
  const handleBack = useCallback(() => {
    router.push('/dashboard/administrador/fleet-orders');
  }, [router]);

  const handleStatusUpdate = useCallback(() => {
    setShowUpdateModal(true);
  }, []);

  const handleDispersionUpdate = useCallback(() => {
    setShowDispersionModal(true);
  }, []);

  const handleModalSuccess = useCallback(() => {
    window.location.reload();
  }, []);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || !order) {
    return null;
  }

  const getStatusBadge = (status: string) => {
    const label = STATUS_LABELS[status as keyof typeof STATUS_LABELS] || status;
    const colorClass = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800';
    
    return (
      <Badge variant="outline" className={colorClass}>
        {label}
      </Badge>
    );
  };

  const getSLAIndicator = () => {
    if (order.sla.isOverdue) {
      return (
        <div className="flex items-center gap-2 text-red-600">
          <AlertCircle size={20} />
          <div>
            <p className="font-medium">SLA Vencido</p>
            <p className="text-sm">{Math.abs(order.sla.daysRemaining)} días de retraso</p>
          </div>
        </div>
      );
    } else if (order.sla.daysRemaining <= 1) {
      return (
        <div className="flex items-center gap-2 text-red-600">
          <AlertCircle size={20} />
          <div>
            <p className="font-medium">SLA Crítico</p>
            <p className="text-sm">{order.sla.daysRemaining} días restantes</p>
          </div>
        </div>
      );
    } else if (order.sla.daysRemaining <= 3) {
      return (
        <div className="flex items-center gap-2 text-yellow-600">
          <AlertCircle size={20} />
          <div>
            <p className="font-medium">SLA Próximo</p>
            <p className="text-sm">{order.sla.daysRemaining} días restantes</p>
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2 text-green-600">
          <CheckCircle size={20} />
          <div>
            <p className="font-medium">En Tiempo</p>
            <p className="text-sm">{order.sla.daysRemaining} días restantes</p>
          </div>
        </div>
      );
    }
  };

  const getEvidenceIcon = (type: string) => {
    switch (type) {
      case 'photo':
        return <Camera size={16} />;
      case 'pdf':
      case 'document':
        return <FileText size={16} />;
      case 'log':
      default:
        return <FileText size={16} />;
    }
  };

  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft size={16} className="mr-2" />
            Volver
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{order.orderNumber}</h1>
            <p className="text-gray-600 mt-1">
              {monthNames[order.month - 1]} {order.year} • {order.totalVehicles || order.totalUnits || 0} vehículos
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={handleStatusUpdate} variant="outline">
            <Edit size={16} className="mr-2" />
            Actualizar Estado
          </Button>
          {(order.status === 'dispersion' || order.dispersion?.length) && (
            <Button onClick={handleDispersionUpdate} className="bg-blue-600 hover:bg-blue-700">
              <MapPin size={16} className="mr-2" />
              {order.dispersion?.length ? 'Editar Dispersión' : 'Configurar Dispersión'}
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Status & SLA */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock size={20} />
                Estado y SLA
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-600 mb-2">Estado Actual</p>
                  {getStatusBadge(order.status)}
                  <p className="text-xs text-gray-500 mt-1">
                    Actualizado: {formatDate(new Date(order.updatedAt))}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">Estado del SLA</p>
                  {getSLAIndicator()}
                  <p className="text-xs text-gray-500 mt-1">
                    Fecha límite: {new Date(order.sla.currentDeadline).toLocaleDateString('es-ES')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Car size={20} />
                Vehículos ({order.vehicles.length} tipos)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.vehicles && order.vehicles.length > 0 ? (
                  order.vehicles.map((vehicle, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">
                          {vehicle.brand} {vehicle.model} {vehicle.version}
                        </p>
                        <p className="text-sm text-gray-600">Dealer: {vehicle.dealer}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{vehicle.quantity} unidades</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">No hay vehículos registrados</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Dispersion */}
          {order.dispersion && order.dispersion.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin size={20} />
                  Dispersión ({order.dispersion.length} ubicaciones)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.dispersion.map((disp, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{disp.city}, {disp.state}</p>
                        <p className="text-sm text-gray-600 flex items-center gap-1">
                          <Calendar size={14} />
                          Entrega: {new Date(disp.deliveryDate).toLocaleDateString('es-ES')}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{disp.quantity} vehículos</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Evidence / Status History */}
          {order.statusHistory && order.statusHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText size={20} />
                  Historial de Estados ({order.statusHistory.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {order.statusHistory.map((historyItem, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 border rounded">
                      {getEvidenceIcon(historyItem.evidence.type)}
                      <div className="flex-1">
                        <p className="font-medium text-sm">{historyItem.evidence.description}</p>
                        <p className="text-xs text-gray-500">
                          Estado: {STATUS_LABELS[historyItem.status] || historyItem.status}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(new Date(historyItem.timestamp))}
                        </p>
                        {/* Show file attachments if available */}
                        {(historyItem.evidence as any).fileUrls && (historyItem.evidence as any).fileUrls.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-600 mb-1">Archivos adjuntos:</p>
                            <div className="flex flex-wrap gap-1">
                              {(historyItem.evidence as any).fileUrls.map((fileUrl: string, fileIndex: number) => (
                                <a
                                  key={fileIndex}
                                  href={fileUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-xs text-blue-600 hover:text-blue-800 underline"
                                >
                                  Ver archivo {fileIndex + 1}
                                </a>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {historyItem.evidence.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Summary & Actions */}
        <div className="space-y-6">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText size={20} />
                Resumen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Período:</span>
                <span className="font-medium">
                  {monthNames[order.month - 1]} {order.year}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Vehículos:</span>
                <span className="font-medium">{order.totalVehicles || order.totalUnits || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Estado:</span>
                {getStatusBadge(order.status)}
              </div>
              <div className="pt-2 border-t">
                <div className="flex justify-between">
                  <span className="text-gray-600">Creado:</span>
                  <span className="text-sm">{formatDate(new Date(order.createdAt))}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-gray-600">Actualizado:</span>
                  <span className="text-sm">{formatDate(new Date(order.updatedAt))}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notification Emails */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail size={20} />
                Correos de Notificación
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Emails automáticos obtenidos de los dealers
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {order.notificationEmails && order.notificationEmails.length > 0 ? (
                  order.notificationEmails.map((email, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <Mail size={14} className="text-gray-400" />
                      <span>{email}</span>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-sm">No hay correos configurados</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          {order.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Notas</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 whitespace-pre-wrap">
                  {order.notes}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Modals */}
      <UpdateStatusModal
        isOpen={showUpdateModal}
        onClose={() => setShowUpdateModal(false)}
        onSuccess={handleModalSuccess}
        order={order}
      />

      <DispersionModal
        isOpen={showDispersionModal}
        onClose={() => setShowDispersionModal(false)}
        onSuccess={handleModalSuccess}
        order={order}
      />
    </div>
  );
}