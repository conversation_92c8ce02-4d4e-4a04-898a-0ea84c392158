export type FleetOrderStatus = 
  | 'created' 
  | 'sent' 
  | 'dispersion' 
  | 'invoice_letter_request' 
  | 'invoice_letter_arrival' 
  | 'supplier_notification' 
  | 'waiting_for_cars' 
  | 'delivered';

export interface Vehicle {
  id?: string;
  brand: string;
  dealer: string;
  model: string;
  version: string;
  quantity: number;
}

export interface Dispersion {
  id?: string;
  state: string;
  city: string;
  quantity: number;
  deliveryDate: string;
  amount: number;
}

export interface Evidence {
  id?: string;
  type: 'log' | 'photo' | 'pdf' | 'document';
  description: string;
  fileUrl?: string;
  fileUrls?: string[];
  uploadedAt?: string;
}

export interface SLA {
  isOverdue: boolean;
  isNear: boolean;
  daysRemaining: number;
  currentDeadline: string;
  status: string;
}

export interface FleetOrder {
  _id?: string;
  id?: string;
  orderNumber: string;
  month: number;
  year: number;
  status: FleetOrderStatus;
  vehicles: Vehicle[];
  dispersion?: Dispersion[];
  notificationEmails: string[];
  evidence?: Evidence[];
  statusHistory?: Array<{
    status: FleetOrderStatus;
    timestamp: string;
    userId: string;
    evidence: {
      type: string;
      description: string;
    };
  }>;
  sla: SLA;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  totalVehicles?: number;
  totalUnits?: number;
}

export interface FleetOrderFilters {
  status?: FleetOrderStatus;
  year?: number;
  month?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateFleetOrderRequest {
  month: number;
  year: number;
  vehicles: Omit<Vehicle, 'id'>[];
  notificationEmails: string[];
}

export interface UpdateFleetOrderStatusRequest {
  status: FleetOrderStatus;
  evidence?: Omit<Evidence, 'id' | 'uploadedAt'>;
  notes?: string;
}

export interface UpdateDispersionRequest {
  dispersion: Omit<Dispersion, 'id'>[];
}

export interface SLAAlert {
  id: string;
  fleetOrderId: string;
  orderNumber: string;
  status: FleetOrderStatus;
  message: string;
  severity: 'warning' | 'critical';
  dueDate: string;
  isResolved: boolean;
  createdAt: string;
}

// Status flow configuration
export const STATUS_FLOW: Record<FleetOrderStatus, FleetOrderStatus | null> = {
  'created': 'sent',
  'sent': 'dispersion',
  'dispersion': 'invoice_letter_request',
  'invoice_letter_request': 'invoice_letter_arrival',
  'invoice_letter_arrival': 'supplier_notification',
  'supplier_notification': 'waiting_for_cars',
  'waiting_for_cars': 'delivered',
  'delivered': null,
};

// Status labels in Spanish
export const STATUS_LABELS: Record<FleetOrderStatus, string> = {
  'created': 'Creada',
  'sent': 'Enviada',
  'dispersion': 'Dispersión',
  'invoice_letter_request': 'Solicitud de Cartas Factura',
  'invoice_letter_arrival': 'Llegada de Cartas Factura',
  'supplier_notification': 'Notificación a Proveedores',
  'waiting_for_cars': 'Esperando Vehículos',
  'delivered': 'Entregada',
};

// Status colors for UI
export const STATUS_COLORS: Record<FleetOrderStatus, string> = {
  'created': 'bg-blue-100 text-blue-800',
  'sent': 'bg-indigo-100 text-indigo-800',
  'dispersion': 'bg-purple-100 text-purple-800',
  'invoice_letter_request': 'bg-yellow-100 text-yellow-800',
  'invoice_letter_arrival': 'bg-orange-100 text-orange-800',
  'supplier_notification': 'bg-pink-100 text-pink-800',
  'waiting_for_cars': 'bg-gray-100 text-gray-800',
  'delivered': 'bg-green-100 text-green-800',
};