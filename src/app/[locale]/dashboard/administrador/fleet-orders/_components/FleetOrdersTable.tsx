'use client';

import { useState } from 'react';
import { DataTableV2 } from '@/components/DataTableV2';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Edit, AlertCircle, CheckCircle } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { FleetOrder, STATUS_LABELS, STATUS_COLORS } from '../types';

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface FleetOrdersTableProps {
  data: FleetOrder[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onRowClick: (order: FleetOrder) => void;
  onStatusUpdate: (order: FleetOrder) => void;
}

export function FleetOrdersTable({
  data,
  pagination,
  onPageChange,
  onRowClick,
  onStatusUpdate
}: FleetOrdersTableProps) {
  const getStatusBadge = (status: string) => {
    const label = STATUS_LABELS[status as keyof typeof STATUS_LABELS] || status;
    const colorClass = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800';
    
    return (
      <Badge variant="outline" className={colorClass}>
        {label}
      </Badge>
    );
  };

  const getSLAIndicator = (order: FleetOrder) => {
    if (order.sla.isOverdue) {
      return (
        <div className="flex items-center gap-1 text-red-600">
          <AlertCircle size={16} />
          <span className="text-sm font-medium">Vencido</span>
        </div>
      );
    } else if (order.sla.daysRemaining <= 1) {
      return (
        <div className="flex items-center gap-1 text-yellow-600">
          <AlertCircle size={16} />
          <span className="text-sm font-medium">Crítico</span>
        </div>
      );
    } else if (order.sla.daysRemaining <= 3) {
      return (
        <div className="flex items-center gap-1 text-orange-600">
          <AlertCircle size={16} />
          <span className="text-sm font-medium">Próximo</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1 text-green-600">
          <CheckCircle size={16} />
          <span className="text-sm font-medium">En tiempo</span>
        </div>
      );
    }
  };

  const columns: ColumnDef<FleetOrder, any>[] = [
    {
      accessorKey: 'orderNumber',
      header: 'Número de Orden',
      cell: ({ row }) => {
        const orderNumber = row.getValue('orderNumber') as string;
        return (
          <div className="font-medium text-blue-600">
            {orderNumber}
          </div>
        );
      },
    },
    {
      accessorKey: 'month',
      header: 'Período',
      cell: ({ row }) => {
        const month = row.getValue('month') as number;
        const year = row.original.year;
        const monthNames = [
          'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
          'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
        ];
        return (
          <div className="font-medium">
            {monthNames[month - 1]} {year}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Estado',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return getStatusBadge(status);
      },
    },
    {
      accessorKey: 'totalVehicles',
      header: 'Vehículos',
      cell: ({ row }) => {
        const order = row.original;
        const total = order.totalVehicles || order.totalUnits || 0;
        return (
          <div className="text-center font-medium">
            {total}
          </div>
        );
      },
    },
    {
      accessorKey: 'sla',
      header: 'SLA',
      cell: ({ row }) => {
        const order = row.original;
        return getSLAIndicator(order);
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Fecha de Creación',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return (
          <div className="text-sm text-gray-600">
            {formatDate(new Date(date))}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Acciones',
      cell: ({ row }) => {
        const order = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onRowClick(order);
              }}
            >
              <Eye size={16} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onStatusUpdate(order);
              }}
            >
              <Edit size={16} />
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      <DataTableV2
        columns={columns as any}
        data={data}
        onRowClick={(row: FleetOrder) => onRowClick(row)}
        pagination={{
          pageIndex: pagination.page - 1,
          pageSize: pagination.limit,
          totalCount: pagination.total,
          onPageChange: (pageIndex: number) => onPageChange(pageIndex + 1)
        }}
      />
    </div>
  );
}