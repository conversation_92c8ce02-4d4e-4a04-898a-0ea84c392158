'use client';

import { useState, useTransition, useRef, useCallback, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Upload, FileText, Camera, AlertCircle, X, File, Plus, Trash2, Calendar, Car, Mail } from 'lucide-react';
import { updateFleetOrderStatus, updateFleetOrderDispersion, updateFleetOrderDetails } from '../_actions/updateFleetOrder';
import { FleetOrder, FleetOrderStatus, STATUS_FLOW, STATUS_LABELS, STATUS_COLORS, CreateFleetOrderRequest } from '../types';
import { toast } from '@/components/ui/use-toast';
import { statesSelect } from '@/constants';
import { Dealer } from '../../dealers/types';
import { URL_API } from '@/constants/api';
import { useSession } from 'next-auth/react';

const dispersionSchema = z.object({
  state: z.string().min(1, 'Estado requerido'),
  city: z.string().min(1, 'Ciudad requerida'),
  quantity: z.number().min(1, 'Cantidad debe ser mayor a 0'),
  deliveryDate: z.string().min(1, 'Fecha de entrega requerida'),
});

const vehicleSchema = z.object({
  brand: z.string().min(1, 'Marca requerida'),
  dealer: z.string().min(1, 'Dealer requerido'),
  model: z.string().min(1, 'Modelo requerido'),
  version: z.string().min(1, 'Versión requerida'),
  quantity: z.number().min(1, 'Cantidad debe ser mayor a 0'),
});

const createFormSchema = (requireEvidence: boolean, isDispersionStatus: boolean, isEditableStatus: boolean) => z.object({
  status: z.enum([
    'created', 'sent', 'dispersion', 'invoice_letter_request',
    'invoice_letter_arrival', 'supplier_notification', 'waiting_for_cars', 'delivered'
  ]),
  evidenceType: requireEvidence ? z.enum(['log', 'photo', 'pdf', 'document']) : z.enum(['log', 'photo', 'pdf', 'document']).optional(),
  evidenceDescription: requireEvidence
    ? z.string().min(1, 'Descripción de evidencia requerida')
    : z.string().optional(),
  notes: z.string().optional(),
  dispersions: isDispersionStatus ? z.array(dispersionSchema).min(1, 'Debe agregar al menos una dispersión') : z.array(dispersionSchema).optional(),
  // Fields for editing when status is "created" (month and year are NOT editable)
  vehicles: isEditableStatus ? z.array(vehicleSchema).min(1, 'Debe agregar al menos un vehículo') : z.array(vehicleSchema).optional(),
  notificationEmails: isEditableStatus ? z.array(z.string().email('Email inválido')).min(1, 'Debe agregar al menos un email') : z.array(z.string().email('Email inválido')).optional(),
});

type FormData = z.infer<ReturnType<typeof createFormSchema>>;

interface UpdateStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  order: FleetOrder | null;
  availableBrands?: string[];
}

export function UpdateStatusModal({ isOpen, onClose, onSuccess, order, availableBrands }: UpdateStatusModalProps) {
  const [isPending, startTransition] = useTransition();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dealersByBrand, setDealersByBrand] = useState<Record<string, Dealer[]>>({});
  const [loadingDealers, setLoadingDealers] = useState<Record<string, boolean>>({});
  const { data: session } = useSession();

  const nextStatus = order ? STATUS_FLOW[order.status] : null;
  const requireEvidence = nextStatus !== 'sent' && order?.status !== 'sent' && nextStatus !== 'dispersion' && order?.status !== 'dispersion'; // Evidence not required when current or next status is 'sent' (Enviada) or 'dispersion'
  const isDispersionStatus = order?.status === 'sent' || order?.status === 'dispersion'; // Show dispersion form when status is 'sent' or 'dispersion'
  const isEditableStatus = order?.status === 'created'; // Allow editing when status is "created"
  const formSchema = createFormSchema(requireEvidence, isDispersionStatus, isEditableStatus);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: nextStatus || order?.status, // Use next status as default
      evidenceType: 'log',
      evidenceDescription: '',
      notes: '',
      dispersions: isDispersionStatus ? (order?.dispersion?.length ?
        order.dispersion.map(d => ({
          state: d.state,
          city: d.city,
          quantity: d.quantity,
          deliveryDate: d.deliveryDate.split('T')[0], // Format date for input
        })) :
        [{
          state: statesSelect[0]?.value || 'Ciudad de México',
          city: '',
          quantity: 1,
          deliveryDate: '',
        }]) : undefined,
      // Default values for editing when status is "created" (month and year are NOT editable)
      vehicles: isEditableStatus ? order?.vehicles?.map(v => ({
        brand: v.brand,
        dealer: v.dealer,
        model: v.model,
        version: v.version,
        quantity: v.quantity,
      })) : undefined,
      notificationEmails: isEditableStatus ? order?.notificationEmails : undefined,
    },
  });

  // Dispersion field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'dispersions',
  });

  // Vehicle field array for editing
  const { fields: vehicleFields, append: appendVehicle, remove: removeVehicle } = useFieldArray({
    control: form.control,
    name: 'vehicles',
  });

  // We'll handle emails differently since they're not a complex object array
  const [emailList, setEmailList] = useState<string[]>([]);

  // Initialize email list when order changes
  useEffect(() => {
    if (isEditableStatus && order?.notificationEmails) {
      setEmailList(order.notificationEmails);
    }
  }, [isEditableStatus, order?.notificationEmails]);

  const watchedDispersions = form.watch('dispersions') || [];
  const watchedVehicles = form.watch('vehicles') || [];

  // Use available brands from dealers or fallback to defaults
  const commonBrands = availableBrands || ['BYD', 'MG', 'Toyota', 'Nissan', 'Volkswagen', 'Chevrolet', 'Ford', 'Otro'];

  // Calculate total vehicles from order
  const calculateTotalVehicles = (order: FleetOrder | null) => {
    if (!order) return 0;
    // Try totalVehicles first, then totalUnits, then calculate from vehicles array
    return order.totalVehicles ||
           order.totalUnits ||
           (order.vehicles?.reduce((sum, vehicle) => sum + (vehicle.quantity || 0), 0)) ||
           0;
  };

  const orderTotalVehicles = calculateTotalVehicles(order);

  // Calculate totals for dispersion
  const totalQuantity = watchedDispersions.reduce((sum, disp) => sum + (disp?.quantity || 0), 0);
  const remainingVehicles = orderTotalVehicles - totalQuantity;

  const handleAddDispersion = () => {
    append({
      state: statesSelect[0]?.value || 'Ciudad de México',
      city: '',
      quantity: Math.max(1, remainingVehicles),
      deliveryDate: '',
    });
  };

  // Helper functions for vehicle editing
  const handleAddVehicle = () => {
    const defaultBrand = commonBrands[0] || 'BYD';
    appendVehicle({
      brand: defaultBrand,
      dealer: '',
      model: '',
      version: '',
      quantity: 1,
    });

    // Fetch dealers for the default brand
    fetchDealersForBrand(defaultBrand);
  };

  const handleAddEmail = () => {
    setEmailList(prev => [...prev, '']);
  };

  const handleRemoveEmail = (index: number) => {
    setEmailList(prev => prev.filter((_, i) => i !== index));
  };

  const handleEmailChange = (index: number, value: string) => {
    setEmailList(prev => {
      const newList = [...prev];
      newList[index] = value;
      return newList;
    });
    // Update form value
    form.setValue('notificationEmails', emailList);
  };

  const fetchDealersForBrand = useCallback(async (brand: string) => {
    if (!session?.user) {
      return;
    }

    setLoadingDealers(prev => {
      if (prev[brand]) {
        return prev;
      }
      return { ...prev, [brand]: true };
    });

    try {
      const user = session.user as any;
      const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/dealers/brand/${brand}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch dealers: ${response.status}`);
      }

      const data = await response.json();
      const dealers = data.data;

      if (dealers) {
        setDealersByBrand(prev => {
          if (prev[brand]) {
            return prev;
          }
          return { ...prev, [brand]: dealers };
        });
      }
    } catch (error) {
      console.error('Error fetching dealers for brand:', brand, error);
      toast({
        title: "Error",
        description: `Error al cargar dealers para ${brand}`,
        variant: "destructive",
      });
    } finally {
      setLoadingDealers(prev => ({ ...prev, [brand]: false }));
    }
  }, [session]);

  const handleBrandChange = async (brand: string, vehicleIndex: number) => {
    form.setValue(`vehicles.${vehicleIndex}.brand`, brand);
    form.setValue(`vehicles.${vehicleIndex}.dealer`, ''); // Reset dealer selection
    form.setValue(`vehicles.${vehicleIndex}.model`, '');
    form.setValue(`vehicles.${vehicleIndex}.version`, '');

    // Fetch dealers for this brand
    await fetchDealersForBrand(brand);

    // If only one dealer exists for this brand, auto-select it
    const dealers = dealersByBrand[brand];
    if (dealers && dealers.length === 1) {
      form.setValue(`vehicles.${vehicleIndex}.dealer`, dealers[0].name);
      updateNotificationEmails();
    }
  };

  const handleDealerChange = (dealerName: string, vehicleIndex: number) => {
    form.setValue(`vehicles.${vehicleIndex}.dealer`, dealerName);
    updateNotificationEmails();
  };

  // Update notification emails based on selected dealers
  const updateNotificationEmails = () => {
    if (!isEditableStatus) return;

    const allEmails = new Set<string>();
    const vehicles = form.getValues('vehicles') || [];

    vehicles.forEach(vehicle => {
      if (vehicle.brand && vehicle.dealer) {
        const dealers = dealersByBrand[vehicle.brand];
        const selectedDealer = dealers?.find(d => d.name === vehicle.dealer);
        if (selectedDealer && selectedDealer.contactEmails) {
          selectedDealer.contactEmails.forEach(email => allEmails.add(email));
        }
      }
    });

    const newEmails = Array.from(allEmails);
    setEmailList(newEmails);
    form.setValue('notificationEmails', newEmails);
  };

  // Fetch dealers for existing vehicles when modal opens in edit mode
  useEffect(() => {
    if (isEditableStatus && order?.vehicles) {
      const uniqueBrands = [...new Set(order.vehicles.map(v => v.brand))];
      uniqueBrands.forEach(brand => {
        fetchDealersForBrand(brand);
      });
    } else if (isEditableStatus && isOpen) {
      // Load dealers for default brand when modal opens for editing
      const defaultBrand = commonBrands[0] || 'BYD';
      fetchDealersForBrand(defaultBrand);
    }
  }, [isEditableStatus, order?.vehicles, fetchDealersForBrand, isOpen, commonBrands]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      const maxSize = 10 * 1024 * 1024; // 10MB
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

      if (file.size > maxSize) {
        toast({
          title: "Error",
          description: `El archivo ${file.name} es demasiado grande. Máximo 10MB.`,
          variant: "destructive",
        });
        return false;
      }

      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Error",
          description: `El archivo ${file.name} no es de un tipo válido. Solo se permiten imágenes, PDFs y documentos de Word.`,
          variant: "destructive",
        });
        return false;
      }

      return true;
    });

    setUploadedFiles(prev => [...prev, ...validFiles]);
    if (event.target) event.target.value = '';
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async (files: File[]): Promise<string[]> => {
    if (files.length === 0) return [];

    setUploading(true);
    const uploadedUrls: string[] = [];

    try {
      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('fleetOrderId', (order?.id || order?._id) ?? '');
        formData.append('evidenceType', form.getValues('evidenceType') ?? 'log');

        // Aquí necesitarías implementar el endpoint de upload
        const response = await fetch('/api/fleet-orders/upload-evidence', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          uploadedUrls.push(result.fileUrl);
        } else {
          throw new Error(`Error uploading ${file.name}`);
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Error al subir algunos archivos",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }

    return uploadedUrls;
  };

  const onSubmit = async (data: FormData) => {
    if (!order) return;

    // Validate dispersion if required
    if (isDispersionStatus && data.dispersions) {
      if (totalQuantity !== orderTotalVehicles) {
        toast({
          title: "Error",
          description: `La cantidad total debe ser ${orderTotalVehicles} vehículos`,
          variant: "destructive",
        });
        return;
      }
    }

    startTransition(async () => {
      try {
        // Handle fleet order details update if status is "created"
        if (isEditableStatus && data.vehicles && order) {
          const updateDetailsRequest: CreateFleetOrderRequest = {
            month: order.month, // Keep original month
            year: order.year,   // Keep original year
            vehicles: data.vehicles,
            notificationEmails: emailList.filter(email => email && email.trim()),
          };

          const detailsResult = await updateFleetOrderDetails((order.id || order._id) ?? '', updateDetailsRequest);

          if (!detailsResult.success) {
            toast({
              title: "Error",
              description: detailsResult.error || 'Error al actualizar los detalles de la orden',
              variant: "destructive",
            });
            return;
          }
        }

        // Upload files first if any
        const fileUrls = await uploadFiles(uploadedFiles);

        // Handle dispersion update if needed
        if (isDispersionStatus && data.dispersions) {
          const dispersions = data.dispersions.map(disp => ({
            state: disp.state,
            city: disp.city,
            quantity: disp.quantity,
            deliveryDate: new Date(disp.deliveryDate).toISOString(),
            amount: 0, // Default amount since it's not collected in UI but still required by API
          }));

          const dispersionResult = await updateFleetOrderDispersion((order.id || order._id) ?? '', { dispersion: dispersions });

          if (!dispersionResult.success) {
            toast({
              title: "Error",
              description: dispersionResult.error || 'Error al actualizar la dispersión',
              variant: "destructive",
            });
            return;
          }
        }

        // Handle status update
        const updateData: any = {
          status: data.status,
          notes: data.notes,
        };

        // Only include evidence if required or if evidence description is provided
        if (requireEvidence || (data.evidenceDescription && data.evidenceDescription.trim())) {
          updateData.evidence = {
            type: data.evidenceType || 'log',
            description: data.evidenceDescription || '',
            fileUrls: fileUrls,
          };
        }

        const result = await updateFleetOrderStatus((order.id || order._id) ?? '', updateData);

        if (result.success) {
          toast({
            title: "Éxito",
            description: isEditableStatus ? "Información de la solicitud actualizada exitosamente" :
                        isDispersionStatus ? "Estado y dispersión actualizados exitosamente" :
                        "Estado actualizado exitosamente",
          });
          form.reset();
          setUploadedFiles([]);
          onSuccess();
          onClose();
        } else {
          toast({
            title: "Error",
            description: result.error || 'Error al actualizar el estado',
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: 'Error inesperado al actualizar el estado',
          variant: "destructive",
        });
        console.error('Error updating status:', error);
      }
    });
  };

  if (!order) return null;

  // Mexican states from constants
  const mexicanStates = statesSelect;

  // Use available brands from dealers or fallback to defaults
  const commonBrands = availableBrands || ['BYD', 'MG', 'Toyota', 'Nissan', 'Volkswagen', 'Chevrolet', 'Ford', 'Otro'];


  const getStatusBadge = (status: FleetOrderStatus) => {
    const label = STATUS_LABELS[status];
    const colorClass = STATUS_COLORS[status];
    return (
      <Badge variant="outline" className={colorClass}>
        {label}
      </Badge>
    );
  };

  const getSLAStatus = () => {
    if (order.sla.isOverdue) {
      return (
        <div className="flex items-center gap-2 text-red-600">
          <AlertCircle size={16} />
          <span className="font-medium">SLA Vencido</span>
          <span className="text-sm">({Math.abs(order.sla.daysRemaining)} días de retraso)</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2 text-green-600">
          <span className="font-medium">En tiempo</span>
          <span className="text-sm">({order.sla.daysRemaining} días restantes)</span>
        </div>
      );
    }
  };

  const getEvidenceIcon = (type: string) => {
    switch (type) {
      case 'photo':
        return <Camera size={16} />;
      case 'pdf':
      case 'document':
        return <FileText size={16} />;
      case 'log':
      default:
        return <FileText size={16} />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText size={20} />
            Actualizar Estado - {order.orderNumber}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Current Status Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Estado Actual</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Estado:</span>
                  {getStatusBadge(order.status)}
                </div>
                <div className="flex items-center justify-between">
                  <span>SLA:</span>
                  {getSLAStatus()}
                </div>
                <div className="flex items-center justify-between">
                  <span>Fecha límite:</span>
                  <span className="font-medium">
                    {new Date(order.sla.currentDeadline).toLocaleDateString('es-MX')}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Edit Order Details - Only show when status is "created" */}
          {isEditableStatus && (
            <>
              {/* Order Information (Read-only) */}
              <Card className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="text-lg text-blue-800">Información de la Orden</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-blue-700">Mes</Label>
                      <div className="p-3 bg-white border border-blue-200 rounded-md">
                        <span className="text-blue-800 font-medium">
                          {order && new Date(2024, order.month - 1, 1).toLocaleDateString('es-MX', { month: 'long' })}
                        </span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-blue-700">Año</Label>
                      <div className="p-3 bg-white border border-blue-200 rounded-md">
                        <span className="text-blue-800 font-medium">{order?.year}</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 text-sm text-blue-600">
                    <p>ℹ️ El mes y año de la orden no pueden ser modificados</p>
                  </div>
                </CardContent>
              </Card>

              {/* Vehicles */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Vehículos</CardTitle>
                    <Button type="button" onClick={handleAddVehicle} variant="outline" size="sm">
                      <Plus size={16} className="mr-2" />
                      Agregar Vehículo
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {vehicleFields && vehicleFields.map((field, index) => (
                      <Card key={field.id} className="border-gray-200">
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <CardTitle className="text-sm">Vehículo {index + 1}</CardTitle>
                            {vehicleFields.length > 1 && (
                              <Button
                                type="button"
                                onClick={() => removeVehicle(index)}
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 size={16} />
                              </Button>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div className="space-y-2">
                              <Label>Marca</Label>
                              <Select
                                value={form.watch(`vehicles.${index}.brand`)}
                                onValueChange={(value) => handleBrandChange(value, index)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Seleccionar marca" />
                                </SelectTrigger>
                                <SelectContent>
                                  {commonBrands.map((brand) => (
                                    <SelectItem key={brand} value={brand}>
                                      {brand}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label>Dealer</Label>
                              <Select
                                value={form.watch(`vehicles.${index}.dealer`)}
                                onValueChange={(value) => handleDealerChange(value, index)}
                                disabled={loadingDealers[form.watch(`vehicles.${index}.brand`)] || !form.watch(`vehicles.${index}.brand`)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder={
                                    loadingDealers[form.watch(`vehicles.${index}.brand`)]
                                      ? "Cargando..."
                                      : "Seleccionar dealer"
                                  } />
                                </SelectTrigger>
                                <SelectContent>
                                  {dealersByBrand[form.watch(`vehicles.${index}.brand`)]?.map((dealer) => (
                                    <SelectItem key={dealer._id} value={dealer.name}>
                                      {dealer.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label>Modelo</Label>
                              <Input
                                {...form.register(`vehicles.${index}.model`)}
                                placeholder="Modelo del vehículo"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Versión</Label>
                              <Input
                                {...form.register(`vehicles.${index}.version`)}
                                placeholder="Versión del vehículo"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Cantidad</Label>
                              <Input
                                type="number"
                                min="1"
                                {...form.register(`vehicles.${index}.quantity`, { valueAsNumber: true })}
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}

                    {form.formState.errors.vehicles && (
                      <p className="text-sm text-red-600">{form.formState.errors.vehicles.message}</p>
                    )}

                    {/* Vehicle Summary */}
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Car size={20} className="text-blue-600" />
                        <span className="font-medium text-blue-800">Resumen de Vehículos</span>
                      </div>
                      <div className="text-sm text-blue-700">
                        <p>Total de vehículos: <span className="font-bold">{watchedVehicles.reduce((sum, vehicle) => sum + (vehicle.quantity || 0), 0)}</span></p>
                        <p>Tipos de vehículos: <span className="font-bold">{vehicleFields?.length || 0}</span></p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Notification Emails */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Emails de Notificación</CardTitle>
                    <Button type="button" onClick={handleAddEmail} variant="outline" size="sm">
                      <Plus size={16} className="mr-2" />
                      Agregar Email
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {emailList.map((email, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="flex-1">
                          <Input
                            value={email}
                            onChange={(e) => handleEmailChange(index, e.target.value)}
                            placeholder="<EMAIL>"
                            type="email"
                          />
                        </div>
                        {emailList.length > 1 && (
                          <Button
                            type="button"
                            onClick={() => handleRemoveEmail(index)}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 size={16} />
                          </Button>
                        )}
                      </div>
                    ))}

                    {form.formState.errors.notificationEmails && (
                      <p className="text-sm text-red-600">{form.formState.errors.notificationEmails.message}</p>
                    )}

                    {/* Email Summary */}
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Mail size={20} className="text-green-600" />
                        <span className="font-medium text-green-800">Emails Configurados</span>
                      </div>
                      <div className="text-sm text-green-700">
                        <p>Total de emails: <span className="font-bold">{emailList.filter(email => email && email.trim()).length}</span></p>
                        {emailList.filter(email => email && email.trim()).length > 0 && (
                          <div className="mt-2">
                            <p className="font-medium">Emails:</p>
                            <ul className="list-disc list-inside ml-2">
                              {emailList.filter(email => email && email.trim()).map((email, index) => (
                                <li key={index} className="text-xs">{email}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* Status Update */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Progreso de Estado</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Nuevo Estado</Label>
                  {nextStatus ? (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-green-800 text-lg">
                            {STATUS_LABELS[nextStatus]}
                          </p>
                          <p className="text-sm text-green-600 mt-1">
                            ✓ Siguiente estado en el flujo normal
                          </p>
                        </div>
                        <Badge className="bg-green-100 text-green-800 border-green-300">
                          Automático
                        </Badge>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-blue-800 text-lg">
                            {STATUS_LABELS[order.status]}
                          </p>
                          <p className="text-sm text-blue-600 mt-1">
                            Esta orden ya está en el estado final
                          </p>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800 border-blue-300">
                          Final
                        </Badge>
                      </div>
                    </div>
                  )}
                  {/* Hidden input to maintain form functionality */}
                  <input
                    type="hidden"
                    {...form.register('status')}
                    value={nextStatus || order.status}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Evidence Section - Only show when evidence is required */}
          {requireEvidence && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Evidencia (Requerida)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="evidenceType">Tipo de Evidencia</Label>
                  <Select
                    value={form.watch('evidenceType')}
                    onValueChange={(value: 'log' | 'photo' | 'pdf' | 'document') =>
                      form.setValue('evidenceType', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="log">
                        <div className="flex items-center gap-2">
                          <FileText size={16} />
                          Log del envío
                        </div>
                      </SelectItem>
                      <SelectItem value="photo">
                        <div className="flex items-center gap-2">
                          <Camera size={16} />
                          Fotografía
                        </div>
                      </SelectItem>
                      <SelectItem value="pdf">
                        <div className="flex items-center gap-2">
                          <FileText size={16} />
                          Documento PDF
                        </div>
                      </SelectItem>
                      <SelectItem value="document">
                        <div className="flex items-center gap-2">
                          <FileText size={16} />
                          Documento
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {nextStatus && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium text-green-600">Recomendado:</span> {STATUS_LABELS[nextStatus]} es el siguiente estado en el flujo normal.
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="evidenceDescription">Descripción de la Evidencia</Label>
                  <Textarea
                    {...form.register('evidenceDescription')}
                    placeholder="Describe qué evidencia se está proporcionando..."
                    rows={3}
                  />
                  {form.formState.errors.evidenceDescription && (
                    <p className="text-sm text-red-600">
                      {form.formState.errors.evidenceDescription.message}
                    </p>
                  )}
                </div>

                {/* File Upload */}
                <div className="space-y-3">
                  <Label>Archivos de Evidencia (Opcional)</Label>

                  {/* Upload Area */}
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload size={32} className="mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600">
                      Haz clic para subir archivos
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Imágenes, PDFs o documentos (máx. 10MB)
                    </p>
                  </div>

                  {/* Hidden File Input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                    onChange={handleFileSelect}
                    className="hidden"
                  />

                  {/* File List */}
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Archivos seleccionados:</p>
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                          <div className="flex items-center gap-2">
                            {file.type.startsWith('image/') ? (
                              <Camera size={16} className="text-blue-500" />
                            ) : (
                              <File size={16} className="text-gray-500" />
                            )}
                            <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                            <span className="text-xs text-gray-500">
                              ({(file.size / 1024).toFixed(1)} KB)
                            </span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-700 p-1"
                          >
                            <X size={16} />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  {uploading && (
                    <div className="text-center py-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-sm text-gray-600 mt-1">Subiendo archivos...</p>
                    </div>
                  )}
                </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Dispersion Section - Only show when dispersion status */}
          {isDispersionStatus && (
            <>
              {/* Order Summary for Dispersion */}
              <Card className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="text-lg text-blue-800">Resumen de la Orden</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-blue-600 font-medium">Total de Vehículos</p>
                      <p className="text-2xl font-bold text-blue-800">{orderTotalVehicles}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-blue-600 font-medium">Vehículos Dispersados</p>
                      <p className={`text-2xl font-bold ${
                        totalQuantity === orderTotalVehicles ? 'text-green-800' :
                        totalQuantity > orderTotalVehicles ? 'text-red-800' : 'text-yellow-800'
                      }`}>
                        {totalQuantity}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-blue-600 font-medium">Vehículos Restantes</p>
                      <p className={`text-2xl font-bold ${
                        remainingVehicles === 0 ? 'text-green-800' :
                        remainingVehicles < 0 ? 'text-red-800' : 'text-yellow-800'
                      }`}>
                        {remainingVehicles}
                      </p>
                    </div>
                  </div>

                  {remainingVehicles !== 0 && (
                    <div className="mt-4 p-3 bg-yellow-100 border border-yellow-200 rounded flex items-center gap-2">
                      <AlertCircle size={16} className="text-yellow-600" />
                      <p className="text-sm text-yellow-800">
                        {remainingVehicles > 0
                          ? `Faltan ${remainingVehicles} vehículos por distribuir`
                          : `Hay ${Math.abs(remainingVehicles)} vehículos de más en la dispersión`
                        }
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Dispersion Details */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Detalle de Dispersión</CardTitle>
                    <Button type="button" onClick={handleAddDispersion} variant="outline" size="sm">
                      <Plus size={16} className="mr-2" />
                      Agregar Ubicación
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {fields && fields.map((field, index) => (
                      <Card key={field.id} className="border-gray-200">
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <CardTitle className="text-sm">Ubicación {index + 1}</CardTitle>
                            {fields.length > 1 && (
                              <Button
                                type="button"
                                onClick={() => remove(index)}
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 size={16} />
                              </Button>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="space-y-2">
                              <Label>Estado</Label>
                              <Select
                                value={form.watch(`dispersions.${index}.state`)}
                                onValueChange={(value) => form.setValue(`dispersions.${index}.state`, value)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Seleccionar estado" />
                                </SelectTrigger>
                                <SelectContent>
                                  {mexicanStates.map((state) => (
                                    <SelectItem key={state.value} value={state.value}>
                                      {state.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label>Ciudad</Label>
                              <Input
                                {...form.register(`dispersions.${index}.city`)}
                                placeholder="Nombre de la ciudad"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Cantidad</Label>
                              <Input
                                type="number"
                                min="1"
                                {...form.register(`dispersions.${index}.quantity`, { valueAsNumber: true })}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Fecha de Entrega</Label>
                              <Input
                                type="date"
                                {...form.register(`dispersions.${index}.deliveryDate`)}
                                min={new Date().toISOString().split('T')[0]}
                                onMouseDown={(e) => {
                                  // Usar mousedown en lugar de click para capturar el gesto del usuario más temprano
                                  try {
                                    const input = e.currentTarget as HTMLInputElement & { showPicker?: () => void };
                                    input.showPicker?.();
                                  } catch (error) {
                                    // Silenciar el error si showPicker no funciona
                                  }
                                }}
                              />
                            </div>
                          </div>

                          {/* Dispersion summary */}
                          <div className="mt-4 p-3 bg-gray-50 rounded border">
                            <div className="text-sm">
                              <span>
                                {watchedDispersions[index]?.quantity || 0} vehículos a{' '}
                                {watchedDispersions[index]?.city || 'ciudad'},{' '}
                                {watchedDispersions[index]?.state || 'estado'}
                              </span>
                            </div>
                            {watchedDispersions[index]?.deliveryDate && (
                              <div className="text-xs text-gray-600 mt-1 flex items-center gap-1">
                                <Calendar size={12} />
                                Entrega: {(() => {
                                  const [year, month, day] = watchedDispersions[index].deliveryDate.split('-');
                                  return new Date(parseInt(year), parseInt(month) - 1, parseInt(day)).toLocaleDateString('es-MX');
                                })()}
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {form.formState.errors.dispersions && (
                    <p className="text-sm text-red-600 mt-2">{form.formState.errors.dispersions.message}</p>
                  )}
                </CardContent>
              </Card>

              {/* Dispersion Summary */}
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="text-lg text-green-800">Resumen de Dispersión</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-green-600 font-medium">Ubicaciones</p>
                      <p className="text-2xl font-bold text-green-800">{fields?.length || 0}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-green-600 font-medium">Total Vehículos</p>
                      <p className="text-2xl font-bold text-green-800">{totalQuantity}</p>
                    </div>
                  </div>

                  {/* Validation status */}
                  <div className="mt-4 flex items-center justify-center">
                    {remainingVehicles === 0 ? (
                      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                        ✓ Dispersión completa y balanceada
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                        ⚠ Dispersión incompleta
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Notas Adicionales (Opcional)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Textarea
                  {...form.register('notes')}
                  placeholder="Agregar notas o comentarios adicionales..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Previous Evidence / Status History */}
          {order.statusHistory && order.statusHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Historial de Estados</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {order.statusHistory.map((historyItem, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 border rounded">
                      {historyItem.evidence && getEvidenceIcon(historyItem.evidence.type)}
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {historyItem.evidence?.description || 'Sin evidencia'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(historyItem.timestamp).toLocaleDateString('es-MX')}
                        </p>
                        <p className="text-xs text-gray-600">
                          Estado: {historyItem.status}
                        </p>
                      </div>
                      {historyItem.evidence && (
                        <Badge variant="outline">{historyItem.evidence.type}</Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isPending}>
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isPending || (!isEditableStatus && !nextStatus) || (isDispersionStatus && remainingVehicles !== 0)}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditableStatus ? 'Actualizando...' : 'Progresando...'}
                </>
              ) : isEditableStatus ? (
                'Actualizar Información'
              ) : nextStatus ? (
                `Avanzar a ${STATUS_LABELS[nextStatus]}`
              ) : (
                'Estado Final Alcanzado'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}