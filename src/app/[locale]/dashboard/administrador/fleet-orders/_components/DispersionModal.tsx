'use client';

import { useState, useTransition } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, MapPin, Calendar, AlertCircle } from 'lucide-react';
import { updateFleetOrderDispersion } from '../_actions/updateFleetOrder';
import { FleetOrder, Dispersion } from '../types';
import { toast } from '@/components/ui/use-toast';
import { statesSelect } from '@/constants';

const dispersionSchema = z.object({
  state: z.string().min(1, 'Estado requerido'),
  city: z.string().min(1, 'Ciudad requerida'),
  quantity: z.number().min(1, 'Cantidad debe ser mayor a 0'),
  deliveryDate: z.string().min(1, 'Fecha de entrega requerida'),
  amount: z.number().min(1, 'Monto debe ser mayor a 0'),
});

const formSchema = z.object({
  dispersions: z.array(dispersionSchema).min(1, 'Debe agregar al menos una dispersión'),
});

type FormData = z.infer<typeof formSchema>;

interface DispersionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  order: FleetOrder | null;
}

export function DispersionModal({ isOpen, onClose, onSuccess, order }: DispersionModalProps) {
  const [isPending, startTransition] = useTransition();
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      dispersions: order?.dispersion?.length ? 
        order.dispersion.map(d => ({
          state: d.state,
          city: d.city,
          quantity: d.quantity,
          deliveryDate: d.deliveryDate.split('T')[0], // Format date for input
          amount: d.amount,
        })) : 
        [{
          state: statesSelect[0]?.value || 'Ciudad de México',
          city: '',
          quantity: 1,
          deliveryDate: '',
          amount: 0,
        }]
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'dispersions',
  });

  const watchedDispersions = form.watch('dispersions');

  // Calculate total vehicles from order
  const calculateTotalVehicles = (order: FleetOrder | null) => {
    if (!order) return 0;
    // Try totalVehicles first, then totalUnits, then calculate from vehicles array
    return order.totalVehicles || 
           order.totalUnits || 
           (order.vehicles?.reduce((sum, vehicle) => sum + (vehicle.quantity || 0), 0)) || 
           0;
  };

  const orderTotalVehicles = calculateTotalVehicles(order);

  // Calculate totals
  const totalQuantity = watchedDispersions.reduce((sum, disp) => sum + (disp.quantity || 0), 0);
  const totalAmount = watchedDispersions.reduce((sum, disp) => sum + (disp.amount || 0), 0);
  const remainingVehicles = orderTotalVehicles - totalQuantity;

  const handleAddDispersion = () => {
    append({
      state: statesSelect[0]?.value || 'Ciudad de México',
      city: '',
      quantity: Math.max(1, remainingVehicles),
      deliveryDate: '',
      amount: 0,
    });
  };

  const onSubmit = (data: FormData) => {
    if (!order) return;

    if (totalQuantity !== orderTotalVehicles) {
      toast({
        title: "Error",
        description: `La cantidad total debe ser ${orderTotalVehicles} vehículos`,
        variant: "destructive",
      });
      return;
    }
    
    startTransition(async () => {
      try {
        const dispersions: Omit<Dispersion, 'id'>[] = data.dispersions.map(disp => ({
          state: disp.state,
          city: disp.city,
          quantity: disp.quantity,
          deliveryDate: new Date(disp.deliveryDate).toISOString(),
          amount: disp.amount,
        }));

        const result = await updateFleetOrderDispersion(order.id || order._id, { dispersion: dispersions });
        
        if (result.success) {
          toast({
            title: "Éxito",
            description: "Dispersión actualizada exitosamente",
          });
          form.reset();
          onSuccess();
          onClose();
        } else {
          toast({
            title: "Error",
            description: result.error || 'Error al actualizar la dispersión',
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: 'Error inesperado al actualizar la dispersión',
          variant: "destructive",
        });
        console.error('Error updating dispersion:', error);
      }
    });
  };

  if (!order) return null;

  // Mexican states from constants
  const mexicanStates = statesSelect;


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin size={20} />
            Configurar Dispersión - {order.orderNumber}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Order Summary */}
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-lg text-blue-800">Resumen de la Orden</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-sm text-blue-600 font-medium">Total de Vehículos</p>
                  <p className="text-2xl font-bold text-blue-800">{orderTotalVehicles}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-blue-600 font-medium">Vehículos Dispersados</p>
                  <p className={`text-2xl font-bold ${
                    totalQuantity === orderTotalVehicles ? 'text-green-800' : 
                    totalQuantity > orderTotalVehicles ? 'text-red-800' : 'text-yellow-800'
                  }`}>
                    {totalQuantity}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-blue-600 font-medium">Vehículos Restantes</p>
                  <p className={`text-2xl font-bold ${
                    remainingVehicles === 0 ? 'text-green-800' : 
                    remainingVehicles < 0 ? 'text-red-800' : 'text-yellow-800'
                  }`}>
                    {remainingVehicles}
                  </p>
                </div>
              </div>
              
              {remainingVehicles !== 0 && (
                <div className="mt-4 p-3 bg-yellow-100 border border-yellow-200 rounded flex items-center gap-2">
                  <AlertCircle size={16} className="text-yellow-600" />
                  <p className="text-sm text-yellow-800">
                    {remainingVehicles > 0 
                      ? `Faltan ${remainingVehicles} vehículos por distribuir`
                      : `Hay ${Math.abs(remainingVehicles)} vehículos de más en la dispersión`
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Dispersion Details */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Detalle de Dispersión</CardTitle>
                <Button type="button" onClick={handleAddDispersion} variant="outline" size="sm">
                  <Plus size={16} className="mr-2" />
                  Agregar Ubicación
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <Card key={field.id} className="border-gray-200">
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm">Ubicación {index + 1}</CardTitle>
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            onClick={() => remove(index)}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 size={16} />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div className="space-y-2">
                          <Label>Estado</Label>
                          <Select
                            value={form.watch(`dispersions.${index}.state`)}
                            onValueChange={(value) => form.setValue(`dispersions.${index}.state`, value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccionar estado" />
                            </SelectTrigger>
                            <SelectContent>
                              {mexicanStates.map((state) => (
                                <SelectItem key={state.value} value={state.value}>
                                  {state.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Ciudad</Label>
                          <Input
                            {...form.register(`dispersions.${index}.city`)}
                            placeholder="Nombre de la ciudad"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Cantidad</Label>
                          <Input
                            type="number"
                            min="1"
                            {...form.register(`dispersions.${index}.quantity`, { valueAsNumber: true })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Fecha de Entrega</Label>
                          <Input
                            type="date"
                            {...form.register(`dispersions.${index}.deliveryDate`)}
                            min={new Date().toISOString().split('T')[0]}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Monto</Label>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            {...form.register(`dispersions.${index}.amount`, { valueAsNumber: true })}
                            placeholder="0.00"
                          />
                        </div>
                      </div>

                      {/* Dispersion summary */}
                      <div className="mt-4 p-3 bg-gray-50 rounded border">
                        <div className="flex justify-between items-center text-sm">
                          <span>
                            {watchedDispersions[index]?.quantity || 0} vehículos a{' '}
                            {watchedDispersions[index]?.city || 'ciudad'},{' '}
                            {watchedDispersions[index]?.state || 'estado'}
                          </span>
                          <span className="font-medium">
                            ${(watchedDispersions[index]?.amount || 0).toLocaleString('es-MX')}
                          </span>
                        </div>
                        {watchedDispersions[index]?.deliveryDate && (
                          <div className="text-xs text-gray-600 mt-1 flex items-center gap-1">
                            <Calendar size={12} />
                            Entrega: {new Date(watchedDispersions[index].deliveryDate).toLocaleDateString('es-ES')}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {form.formState.errors.dispersions && (
                <p className="text-sm text-red-600 mt-2">{form.formState.errors.dispersions.message}</p>
              )}
            </CardContent>
          </Card>

          {/* Dispersion Summary */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="text-lg text-green-800">Resumen de Dispersión</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-sm text-green-600 font-medium">Ubicaciones</p>
                  <p className="text-2xl font-bold text-green-800">{fields.length}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-green-600 font-medium">Total Vehículos</p>
                  <p className="text-2xl font-bold text-green-800">{totalQuantity}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-green-600 font-medium">Monto Total</p>
                  <p className="text-2xl font-bold text-green-800">
                    ${totalAmount.toLocaleString('es-MX')}
                  </p>
                </div>
              </div>

              {/* Validation status */}
              <div className="mt-4 flex items-center justify-center">
                {remainingVehicles === 0 ? (
                  <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                    ✓ Dispersión completa y balanceada
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    ⚠ Dispersión incompleta
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isPending}>
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={isPending || remainingVehicles !== 0} 
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Actualizando...
                </>
              ) : (
                'Actualizar Dispersión'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}