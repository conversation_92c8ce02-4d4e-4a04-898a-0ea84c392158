'use client';

import { useState, useTransition, useEffect, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Car, Mail, AlertCircle, Plus, Trash2 } from 'lucide-react';
import { createFleetOrder } from '../_actions/createFleetOrder';
import { CreateFleetOrderRequest } from '../types';
import { toast } from '@/components/ui/use-toast';
import { Dealer } from '../../dealers/types';
import { URL_API } from '@/constants/api';
import { useSession } from 'next-auth/react';

const vehicleSchema = z.object({
  brand: z.string().min(1, 'Marca requerida'),
  dealer: z.string().min(1, 'Dealer requerido'),
  model: z.string().min(1, 'Modelo requerido'),
  version: z.string().min(1, 'Versión requerida'),
  quantity: z.number().min(1, 'Cantidad debe ser mayor a 0'),
});

const formSchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(2020).max(2030),
  vehicles: z.array(vehicleSchema).min(1, 'Debe agregar al menos un vehículo'),
  notificationEmails: z.array(z.string().email('Email inválido')).min(1, 'Debe agregar al menos un email'),
});

type FormData = z.infer<typeof formSchema>;

interface CreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  availableBrands?: string[];
}

export function CreateOrderModal({ isOpen, onClose, onSuccess, availableBrands }: CreateOrderModalProps) {
  const [isPending, startTransition] = useTransition();
  const [dealersByBrand, setDealersByBrand] = useState<Record<string, Dealer[]>>({});
  const [loadingDealers, setLoadingDealers] = useState<Record<string, boolean>>({});
  const { data: session } = useSession();
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      vehicles: [
        {
          brand: 'BYD',
          dealer: '',
          model: '',
          version: '',
          quantity: 1,
        }
      ],
      notificationEmails: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'vehicles',
  });

  const watchedVehicles = form.watch('vehicles');
  const watchedEmails = form.watch('notificationEmails');

  // Calculate totals
  const totalVehicles = watchedVehicles.reduce((sum, vehicle) => sum + (vehicle.quantity || 0), 0);

  const handleAddVehicle = () => {
    append({
      brand: 'BYD',
      dealer: '',
      model: '',
      version: '',
      quantity: 1,
    });
    
    // Fetch dealers for the default brand
    fetchDealersForBrand('BYD');
  };


  const fetchDealersForBrand = useCallback(async (brand: string) => {
    if (!session?.user) {
      return;
    }

    setLoadingDealers(prev => {
      if (prev[brand]) {
        return prev;
      }
      return { ...prev, [brand]: true };
    });

    try {
      const user = session.user as any;
      const response = await fetch(`${URL_API}/vendor-platform/fleet-orders/dealers/brand/${brand}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch dealers: ${response.status}`);
      }

      const data = await response.json();
      const dealers = data.data;
      
      if (dealers) {
        setDealersByBrand(prev => {
          if (prev[brand]) {
            return prev;
          }
          return { ...prev, [brand]: dealers };
        });
      }
    } catch (error) {
      console.error('Error fetching dealers for brand:', brand, error);
      toast({
        title: "Error",
        description: `Error al cargar dealers para ${brand}`,
        variant: "destructive",
      });
    } finally {
      setLoadingDealers(prev => ({ ...prev, [brand]: false }));
    }
  }, [session]);

  const handleBrandChange = async (brand: string, vehicleIndex: number) => {
    form.setValue(`vehicles.${vehicleIndex}.brand`, brand);
    form.setValue(`vehicles.${vehicleIndex}.dealer`, ''); // Reset dealer selection
    
    // Fetch dealers for this brand
    await fetchDealersForBrand(brand);
    
    // If only one dealer exists for this brand, auto-select it
    const dealers = dealersByBrand[brand];
    if (dealers && dealers.length === 1) {
      form.setValue(`vehicles.${vehicleIndex}.dealer`, dealers[0].name);
      updateNotificationEmails();
    }
  };

  const handleDealerChange = (dealerName: string, vehicleIndex: number) => {
    form.setValue(`vehicles.${vehicleIndex}.dealer`, dealerName);
    updateNotificationEmails();
  };

  // Update notification emails based on selected dealers
  const updateNotificationEmails = () => {
    const allEmails = new Set<string>();
    const vehicles = form.getValues('vehicles');
    
    vehicles.forEach(vehicle => {
      if (vehicle.brand && vehicle.dealer) {
        const dealers = dealersByBrand[vehicle.brand];
        const selectedDealer = dealers?.find(d => d.name === vehicle.dealer);
        if (selectedDealer && selectedDealer.contactEmails) {
          selectedDealer.contactEmails.forEach(email => allEmails.add(email));
        }
      }
    });
    
    form.setValue('notificationEmails', Array.from(allEmails));
  };

  const onSubmit = (data: FormData) => {
    startTransition(async () => {
      try {
        const request: CreateFleetOrderRequest = {
          month: data.month,
          year: data.year,
          vehicles: data.vehicles,
          notificationEmails: data.notificationEmails,
        };

        const result = await createFleetOrder(request);
        
        if (result.success) {
          toast({
            title: "Éxito",
            description: "Orden de flota creada exitosamente",
          });
          form.reset();
          onSuccess();
          onClose();
        } else {
          toast({
            title: "Error",
            description: result.error || 'Error al crear la orden',
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: 'Error inesperado al crear la orden',
          variant: "destructive",
        });
        console.error('Error creating fleet order:', error);
      }
    });
  };

  const months = [
    { value: 1, label: 'Enero' },
    { value: 2, label: 'Febrero' },
    { value: 3, label: 'Marzo' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Mayo' },
    { value: 6, label: 'Junio' },
    { value: 7, label: 'Julio' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Septiembre' },
    { value: 10, label: 'Octubre' },
    { value: 11, label: 'Noviembre' },
    { value: 12, label: 'Diciembre' }
  ];

  // Use available brands from dealers or fallback to defaults
  const commonBrands = availableBrands || ['BYD', 'MG', 'Toyota', 'Nissan', 'Volkswagen', 'Chevrolet', 'Ford', 'Otro'];

  // Load dealers for default brand when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchDealersForBrand('BYD');
    }
  }, [isOpen, fetchDealersForBrand]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Car size={20} />
            Nueva Orden de Flota
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Period Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Período de la Orden</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="month">Mes</Label>
                  <Select
                    value={form.watch('month')?.toString()}
                    onValueChange={(value) => form.setValue('month', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar mes" />
                    </SelectTrigger>
                    <SelectContent>
                      {months.map((month) => (
                        <SelectItem key={month.value} value={month.value.toString()}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.month && (
                    <p className="text-sm text-red-600">{form.formState.errors.month.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="year">Año</Label>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    {...form.register('year', { valueAsNumber: true })}
                  />
                  {form.formState.errors.year && (
                    <p className="text-sm text-red-600">{form.formState.errors.year.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicles Section */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Vehículos</CardTitle>
                <Button type="button" onClick={handleAddVehicle} variant="outline" size="sm">
                  <Plus size={16} className="mr-2" />
                  Agregar Vehículo
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <Card key={field.id} className="border-gray-200">
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm">Vehículo {index + 1}</CardTitle>
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            onClick={() => remove(index)}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 size={16} />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Marca</Label>
                          <Select
                            value={form.watch(`vehicles.${index}.brand`)}
                            onValueChange={(value) => handleBrandChange(value, index)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccionar marca" />
                            </SelectTrigger>
                            <SelectContent>
                              {commonBrands.map((brand) => (
                                <SelectItem key={brand} value={brand}>
                                  {brand}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Dealer</Label>
                          <Select
                            value={form.watch(`vehicles.${index}.dealer`)}
                            onValueChange={(value) => handleDealerChange(value, index)}
                            disabled={!form.watch(`vehicles.${index}.brand`) || loadingDealers[form.watch(`vehicles.${index}.brand`)]}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={
                                !form.watch(`vehicles.${index}.brand`) 
                                  ? "Primero selecciona una marca" 
                                  : loadingDealers[form.watch(`vehicles.${index}.brand`)] 
                                    ? "Cargando dealers..." 
                                    : "Seleccionar dealer"
                              } />
                            </SelectTrigger>
                            <SelectContent>
                              {(() => {
                                const currentBrand = form.watch(`vehicles.${index}.brand`);
                                const dealers = dealersByBrand[currentBrand];
                                return dealers?.map((dealer) => (
                                  <SelectItem key={dealer._id} value={dealer.name}>
                                    {dealer.name}
                                  </SelectItem>
                                )) || null;
                              })()}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Modelo</Label>
                          <Input
                            {...form.register(`vehicles.${index}.model`)}
                            placeholder="Modelo del vehículo"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Versión</Label>
                          <Input
                            {...form.register(`vehicles.${index}.version`)}
                            placeholder="Versión/trim"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Cantidad</Label>
                          <Input
                            type="number"
                            min="1"
                            {...form.register(`vehicles.${index}.quantity`, { valueAsNumber: true })}
                          />
                        </div>

                      </div>

                    </CardContent>
                  </Card>
                ))}
              </div>

              {form.formState.errors.vehicles && (
                <p className="text-sm text-red-600 mt-2">{form.formState.errors.vehicles.message}</p>
              )}
            </CardContent>
          </Card>

          {/* Notification Emails - Auto-generated from Dealers */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Mail size={20} />
                Correos de Notificación
              </CardTitle>
              <p className="text-sm text-gray-600">
                Emails automáticos obtenidos de los dealers seleccionados
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {watchedEmails.length > 0 ? (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700">
                      Se notificará a ({watchedEmails.length} emails):
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {watchedEmails.map((email, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-2">
                          <Mail size={14} />
                          {email}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center gap-2 text-amber-800">
                      <AlertCircle size={16} />
                      <p className="text-sm font-medium">No hay emails de notificación</p>
                    </div>
                    <p className="text-sm text-amber-700 mt-1">
                      Selecciona dealers para obtener automáticamente sus emails de notificación
                    </p>
                  </div>
                )}

                {form.formState.errors.notificationEmails && (
                  <p className="text-sm text-red-600">{form.formState.errors.notificationEmails.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-lg text-blue-800">Resumen de la Orden</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-sm text-blue-600 font-medium">Total de Vehículos</p>
                  <p className="text-2xl font-bold text-blue-800">{totalVehicles}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-blue-600 font-medium">Emails de Notificación</p>
                  <p className="text-2xl font-bold text-blue-800">{watchedEmails.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isPending}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isPending} className="bg-blue-600 hover:bg-blue-700">
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creando...
                </>
              ) : (
                'Crear Orden'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}