'use client';

import { useState, useCallback, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, TrendingUp, Clock, AlertTriangle } from 'lucide-react';
import { FleetOrdersTable } from './_components/FleetOrdersTable';
import { FleetOrderFiltersComponent } from './_components/FleetOrderFilters';
import { CreateOrderModal } from './_components/CreateOrderModal';
import { UpdateStatusModal } from './_components/UpdateStatusModal';
import { FleetOrder, FleetOrderFilters, SLAAlert } from './types';
import { useRouter, useSearchParams } from 'next/navigation';

interface FleetOrdersResponse {
  data: FleetOrder[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface FleetOrdersClientProps {
  initialData: FleetOrdersResponse | null;
  initialFilters: FleetOrderFilters;
  slaAlerts?: SLAAlert[];
  availableBrands?: string[];
}

export default function FleetOrdersClient({ 
  initialData, 
  initialFilters,
  slaAlerts = [],
  availableBrands = []
}: FleetOrdersClientProps) {
  console.log('FleetOrdersClient initialData:', initialData);
  console.log('FleetOrdersClient initialFilters:', initialFilters);
  console.log('Total from API:', initialData?.total, 'Data length:', initialData?.data?.length);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  
  const [data, setData] = useState<FleetOrdersResponse | null>(initialData);
  const [filters, setFilters] = useState<FleetOrderFilters>(initialFilters);
  const [selectedOrder, setSelectedOrder] = useState<FleetOrder | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  // Statistics from the data
  const totalOrders = data?.total || data?.data?.length || 0;
  const overdueOrders = data?.data.filter(order => order.sla.isOverdue).length || 0;
  const warningOrders = data?.data.filter(order => 
    !order.sla.isOverdue && order.sla.daysRemaining <= 3
  ).length || 0;
  
  // Calculate total vehicles
  const totalVehicles = data?.data.reduce((sum, order) => sum + (order.totalVehicles || order.totalUnits || 0), 0) || 0;

  const updateURLWithFilters = useCallback((newFilters: FleetOrderFilters) => {
    const params = new URLSearchParams(searchParams);
    
    // Update URL parameters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      } else {
        params.delete(key);
      }
    });

    startTransition(() => {
      router.push(`?${params.toString()}`, { scroll: false });
    });
  }, [searchParams, router]);

  const handleFiltersChange = useCallback((newFilters: FleetOrderFilters) => {
    setFilters(newFilters);
    updateURLWithFilters(newFilters);
  }, [updateURLWithFilters]);

  const handleClearFilters = useCallback(() => {
    const clearedFilters: FleetOrderFilters = { page: 1, limit: 10 };
    setFilters(clearedFilters);
    updateURLWithFilters(clearedFilters);
  }, [updateURLWithFilters]);

  const handlePageChange = useCallback((page: number) => {
    const newFilters = { ...filters, page };
    handleFiltersChange(newFilters);
  }, [filters, handleFiltersChange]);

  const handleRowClick = useCallback((order: FleetOrder) => {
    // Navigate to order details page
    const orderId = order.id || (order as any)._id;
    if (orderId) {
      router.push(`/dashboard/administrador/fleet-orders/${orderId}`);
    } else {
      console.error('No ID found in order object');
    }
  }, [router]);

  const handleStatusUpdate = useCallback((order: FleetOrder) => {
    setSelectedOrder(order);
    setShowUpdateModal(true);
  }, []);

  const handleCreateOrder = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleModalSuccess = useCallback(() => {
    // Refresh the page to get updated data
    window.location.reload();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Fleet Orders</h1>
          <p className="text-gray-600 mt-2">
            Gestión y control de órdenes de flota vehicular
          </p>
        </div>
        
        <Button onClick={handleCreateOrder} className="bg-blue-600 hover:bg-blue-700">
          <Plus size={16} className="mr-2" />
          Nueva Orden
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Órdenes</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              {totalVehicles} vehículos totales
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Órdenes Activas</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data?.data.filter(order => order.status !== 'delivered').length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Órdenes en proceso
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SLA Crítico</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{warningOrders}</div>
            <p className="text-xs text-muted-foreground">
              Próximos a vencer
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SLA Vencido</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{overdueOrders}</div>
            <p className="text-xs text-muted-foreground">
              Requieren atención
            </p>
          </CardContent>
        </Card>
      </div>

      {/* SLA Alerts */}
      {slaAlerts.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertTriangle size={20} />
              Alertas de SLA ({slaAlerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {slaAlerts.slice(0, 3).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-2 bg-white rounded border">
                  <div>
                    <p className="font-medium text-sm">{alert.orderNumber}</p>
                    <p className="text-xs text-gray-600">{alert.message}</p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    alert.severity === 'critical' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {alert.severity === 'critical' ? 'Crítico' : 'Advertencia'}
                  </div>
                </div>
              ))}
              {slaAlerts.length > 3 && (
                <p className="text-sm text-gray-600 text-center mt-2">
                  +{slaAlerts.length - 3} alertas más
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <FleetOrderFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          {data && data.data.length > 0 ? (
            <FleetOrdersTable
              data={data.data}
              pagination={{
                total: data.total,
                page: data.page,
                limit: data.limit,
                totalPages: data.totalPages
              }}
              onPageChange={handlePageChange}
              onRowClick={handleRowClick}
              onStatusUpdate={handleStatusUpdate}
            />
          ) : (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-4">
                <TrendingUp size={48} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No hay órdenes de flota
              </h3>
              <p className="text-gray-600 mb-4">
                Comienza creando tu primera orden de flota para este período.
              </p>
              <Button onClick={handleCreateOrder} className="bg-blue-600 hover:bg-blue-700">
                <Plus size={16} className="mr-2" />
                Crear Primera Orden
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {isPending && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </div>
      )}

      {/* Modals */}
      <CreateOrderModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleModalSuccess}
        availableBrands={availableBrands}
      />

      <UpdateStatusModal
        isOpen={showUpdateModal}
        onClose={() => setShowUpdateModal(false)}
        onSuccess={handleModalSuccess}
        order={selectedOrder}
      />
    </div>
  );
}