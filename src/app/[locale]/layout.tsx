import SessionProvider from "../../Providers/SessionProvider";
import Cha<PERSON><PERSON>rovider from "@/Providers/ChakraProvider";
import { Inter } from "next/font/google";
import { Metadata } from "next";
import icon from "@/assets/OCNLogo.svg";
import { Toaster } from "@/components/ui/toaster";
import { cn } from "@/lib/utils";
import { I18nProviderClient } from "@/i18n/client";
import "./globals.css";
import ReactQueryProvider from '@/Providers/ReactQuery';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "OCN Vendor Platform",
  description: "OCN Vendor Platform",
  icons: {
    icon: icon.src,
  },
  manifest: "/manifest.json",
};

export default async function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <html lang={locale} suppressHydrationWarning={true}
      className='!min-h-[100svh] min-w-100vw'
    >
      <SessionProvider>
        <I18nProviderClient locale={locale}>
          <ReactQueryProvider>
            <body className={cn(inter.className, "min-h-[100vh] min-w-100vw h-full")}>
            <ChakraProvider>{children}</ChakraProvider>
            <Toaster />
          </body>
          </ReactQueryProvider>
        </I18nProviderClient>
      </SessionProvider>
    </html>
  );
}
