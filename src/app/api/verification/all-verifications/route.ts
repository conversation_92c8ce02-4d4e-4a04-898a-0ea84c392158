import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user as any;

    // Verificar permisos - el usuario debe tener isAdmin: true en la plataforma de vendor
    if (!user.isAdmin && !['superAdmin', 'company-gestor', 'workshop', 'gestor', 'eco'].includes(user.userType)) {
      return NextResponse.json({ error: 'User not authorized for admin operations' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page');
    const limit = searchParams.get('limit');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const query = searchParams.get('query');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Llamar al backend real usando endpoint de vendor
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
    const url = new URL('/vendor-platform/emissions-verification/vendor/verifications', API_BASE_URL);

    // Agregar parámetros de query
    if (page) url.searchParams.set('page', page);
    if (limit) url.searchParams.set('limit', limit);
    if (status) url.searchParams.set('status', status);
    if (search) url.searchParams.set('search', search);
    if (query) url.searchParams.set('query', query);
    if (startDate) url.searchParams.set('startDate', startDate);
    if (endDate) url.searchParams.set('endDate', endDate);

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json({ error: 'Failed to fetch verifications' }, { status: response.status });
    }

    const result = await response.json();
    
    // Asegurar que el resultado sea serializable
    const serializedData = JSON.parse(JSON.stringify(result.data || result));
    return NextResponse.json(serializedData);
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
