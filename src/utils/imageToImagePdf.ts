import jsPDF from 'jspdf';

export interface ImageToPdfOptions {
  quality?: number; // 1-100, default 80
  maxWidth?: number; // max width in mm, default 190
  maxHeight?: number; // max height in mm, default 270
}

/**
 * Converts an image file to a PDF file
 * @param imageFile - The image file to convert
 * @param options - Conversion options
 * @returns Promise that resolves to a PDF File object
 */
export const convertImageToPdf = async (
  imageFile: File,
  options: ImageToPdfOptions = {}
): Promise<File> => {
  const { quality = 80, maxWidth = 190, maxHeight = 270 } = options;

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const img = new Image();
      
      img.onload = () => {
        try {
          // Create a new jsPDF instance
          const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
          });

          // Calculate dimensions to fit within the page while maintaining aspect ratio
          const imgWidth = img.width;
          const imgHeight = img.height;
          const aspectRatio = imgWidth / imgHeight;

          let pdfWidth = maxWidth;
          let pdfHeight = maxWidth / aspectRatio;

          // If height exceeds max height, scale based on height
          if (pdfHeight > maxHeight) {
            pdfHeight = maxHeight;
            pdfWidth = maxHeight * aspectRatio;
          }

          // Center the image on the page
          const x = (210 - pdfWidth) / 2; // A4 width is 210mm
          const y = (297 - pdfHeight) / 2; // A4 height is 297mm

          // Add the image to the PDF
          pdf.addImage(
            event.target?.result as string,
            'JPEG',
            x,
            y,
            pdfWidth,
            pdfHeight,
            undefined,
            'FAST'
          );

          // Generate PDF as blob
          const pdfBlob = pdf.output('blob');

          // Create a new File object with the original filename but .pdf extension
          const originalName = imageFile.name.replace(/\.[^/.]+$/, '');
          const pdfFile = new File([pdfBlob], `${originalName}.pdf`, {
            type: 'application/pdf',
            lastModified: Date.now()
          });

          resolve(pdfFile);
        } catch (error) {
          reject(new Error(`Error converting image to PDF: ${error}`));
        }
      };

      img.onerror = () => {
        reject(new Error('Error loading image'));
      };

      img.src = event.target?.result as string;
    };

    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };

    reader.readAsDataURL(imageFile);
  });
};

/**
 * Checks if a file is an image that can be converted to PDF
 * @param file - The file to check
 * @returns boolean indicating if the file is a convertible image
 */
export const isConvertibleImage = (file: File): boolean => {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
  const fileName = file.name.toLowerCase();
  return imageTypes.includes(file.type.toLowerCase()) || 
         fileName.endsWith('.heic') || 
         fileName.endsWith('.heif');
};

/**
 * Processes a file, converting it to PDF if it's an image, otherwise returns the original file
 * @param file - The file to process
 * @param options - Conversion options for images
 * @returns Promise that resolves to either the original file or a converted PDF
 */
export const processFileForUpload = async (
  file: File,
  options: ImageToPdfOptions = {}
): Promise<File> => {
  // If it's already a PDF, return as is
  if (file.type === 'application/pdf') {
    return file;
  }

  // If it's an image, convert to PDF
  if (isConvertibleImage(file)) {
    return await convertImageToPdf(file, options);
  }

  // For any other file type, return as is
  return file;
};